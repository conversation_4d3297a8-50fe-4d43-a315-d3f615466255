# 签到功能接口集成测试文档

## 修改内容总结

### 1. 后端修改

#### 1.1 WeChatSigninController.java
- **修改 `/info` 接口**：返回统一的签到信息对象，包含今日签到状态、连续签到天数、总签到天数等
- **修改 `/do` 接口**：优化签到逻辑，返回标准化的签到结果数据
- **添加 HashMap 导入**：支持返回 Map 类型数据

#### 1.2 接口返回数据结构

**获取签到信息 (`GET /blade-chat/signin/info`)**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "todaySigned": false,
    "continuousDays": 5,
    "totalDays": 30,
    "lastSigninDate": "2024-01-01",
    "signinReward": 10,
    "continuousRewards": [
      {"days": 1, "reward": 10},
      {"days": 3, "reward": 15},
      {"days": 7, "reward": 25},
      {"days": 15, "reward": 50},
      {"days": 30, "reward": 100}
    ]
  }
}
```

**执行签到 (`POST /blade-chat/signin/do`)**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "reward": 10,
    "continuousReward": 15,
    "continuousDays": 6,
    "totalDays": 31,
    "currentPoints": 175
  }
}
```

### 2. 前端修改

#### 2.1 signin.js
- **优化数据处理**：正确解析后端返回的数据结构
- **增强错误处理**：添加网络错误和业务错误的处理
- **改进签到逻辑**：实时更新页面状态，避免重复请求
- **完善日历功能**：生成完整的月份日历数据

#### 2.2 signin.wxml
- **添加加载状态**：在数据加载时显示占位符
- **防空处理**：使用默认值避免显示 undefined

## 测试步骤

### 1. 环境准备
1. 确保后端服务正常运行
2. 确保用户已登录并获得有效 token
3. 确保签到相关数据库表已创建

### 2. 功能测试

#### 2.1 页面加载测试
1. 打开签到页面
2. 检查是否正确显示：
   - 用户积分
   - 今日签到状态
   - 连续签到天数
   - 本月签到记录日历

#### 2.2 签到功能测试
1. **首次签到**：
   - 点击"立即签到"按钮
   - 检查是否显示签到成功提示
   - 检查积分是否增加
   - 检查签到状态是否更新为"已签到"

2. **重复签到**：
   - 再次点击签到按钮
   - 应该无法重复签到

3. **连续签到奖励**：
   - 连续多天签到
   - 检查是否触发连续签到奖励弹窗
   - 检查奖励积分是否正确发放

#### 2.3 日历显示测试
1. 检查当月日历是否正确显示
2. 检查已签到日期是否有标记
3. 检查今日日期是否高亮显示

### 3. 异常情况测试

#### 3.1 网络异常
1. 断开网络连接
2. 尝试签到或刷新页面
3. 检查是否显示合适的错误提示

#### 3.2 服务器异常
1. 停止后端服务
2. 尝试操作
3. 检查错误处理是否正常

#### 3.3 数据异常
1. 清空本地存储
2. 重新进入页面
3. 检查是否能正常加载默认数据

## 预期结果

### 1. 正常流程
- 页面加载时显示正确的用户积分和签到状态
- 签到成功后立即更新页面数据
- 连续签到时正确计算和显示奖励
- 日历正确显示当月签到记录

### 2. 异常处理
- 网络错误时显示友好的错误提示
- 服务器错误时不会导致页面崩溃
- 数据缺失时使用合理的默认值

## 常见问题排查

### 1. 签到状态不更新
- 检查后端接口返回数据格式
- 检查前端数据绑定是否正确
- 检查是否有缓存问题

### 2. 积分显示错误
- 检查积分接口是否正常
- 检查数据类型转换是否正确
- 检查是否有并发更新问题

### 3. 日历显示异常
- 检查日期计算逻辑
- 检查数据格式是否匹配
- 检查样式是否正确应用

## 性能优化建议

1. **缓存策略**：合理缓存签到状态，避免频繁请求
2. **懒加载**：日历数据可以按需加载
3. **防抖处理**：签到按钮添加防抖，避免重复提交
4. **错误重试**：网络错误时提供重试机制

## 部署清单

### 1. 后端文件修改
- ✅ `WeChatSigninController.java` - 修改签到接口返回数据结构
- ✅ `WeChatPointsInfoController.java` - 新增积分信息接口

### 2. 前端文件修改
- ✅ `signin.js` - 优化数据处理和错误处理
- ✅ `signin.wxml` - 添加加载状态和防空处理

### 3. 接口依赖检查
- 确保 `ISigninService` 服务已实现以下方法：
  - `getSigninStats(String userId)`
  - `getContinuousDays(String userId)`
  - `getContinuousRewards()`
  - `doSignin(SigninDTO signinDTO)`
  - `getMonthSigninRecord(String userId, Integer year, Integer month)`
- 确保 `IWeUserService` 服务已实现：
  - `getPoints(Long userId)`

### 4. 数据库表检查
- 确保签到相关表已创建：
  - `urb_signin_record` - 签到记录表
  - 用户积分相关表

## 接口测试用例

### 1. 获取签到信息接口
```bash
curl -X GET "http://localhost:8080/blade-chat/signin/info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "todaySigned": false,
    "continuousDays": 0,
    "totalDays": 0,
    "lastSigninDate": null,
    "signinReward": 10,
    "continuousRewards": [
      {"days": 1, "reward": 10},
      {"days": 3, "reward": 15},
      {"days": 7, "reward": 25},
      {"days": 15, "reward": 50},
      {"days": 30, "reward": 100}
    ]
  }
}
```

### 2. 执行签到接口
```bash
curl -X POST "http://localhost:8080/blade-chat/signin/do" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "reward": 10,
    "continuousReward": 0,
    "continuousDays": 1,
    "totalDays": 1,
    "currentPoints": 10
  }
}
```

### 3. 获取积分信息接口
```bash
curl -X GET "http://localhost:8080/miniapp/points/info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**预期响应：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": "123456",
    "points": 10,
    "totalEarned": 0,
    "totalSpent": 0,
    "level": 1,
    "levelName": "初级用户",
    "experience": 0,
    "nextLevelExp": 100
  }
}
```

### 4. 获取月签到记录接口
```bash
curl -X GET "http://localhost:8080/blade-chat/signin/record?year=2024&month=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 上线前检查清单

- [ ] 后端服务正常启动
- [ ] 数据库连接正常
- [ ] 所有接口返回正确的数据格式
- [ ] 前端页面能正常加载和显示数据
- [ ] 签到功能正常工作
- [ ] 错误处理机制正常
- [ ] 日志记录完整
- [ ] 性能测试通过

## 监控指标

1. **业务指标**
   - 签到成功率
   - 用户活跃度
   - 连续签到用户数
   - 积分发放总量

2. **技术指标**
   - 接口响应时间
   - 错误率
   - 并发处理能力
   - 数据库性能
