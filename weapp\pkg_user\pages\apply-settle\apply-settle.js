// pkg_user/pages/apply-settle/apply-settle.js
const { applyInstitution, getInstitutionTypes } = require('../../api/institution.js')

Page({
  data: {
    // 申请表单数据
    formData: {
      name: '', // 机构名称
      typeId: '', // 机构分类ID
      contactPerson: '', // 联系人姓名
      phone: '', // 联系电话
      email: '', // 电子邮箱
      wechat: '', // 微信号
      licenseNo: '', // 营业执照号码
      licenseImage: '', // 营业执照照片
      legalPerson: '', // 法人代表姓名
      province: '', // 省
      city: '', // 市
      district: '', // 区
      detailAddress: '', // 详细地址
      description: '', // 机构简介
      images: '', // 商家图片
      businessHours: '', // 营业时间
      specialServices: '', // 特色服务
      paymentMethods: '', // 支付方式
      isStore: true, // 是否支持到店
      hasDelivery: false, // 是否支持外卖
      serviceRadius: 5000 // 服务半径(米)
    },
    // 机构分类列表
    institutionTypes: [],
    // 提交状态
    submitting: false
  },

  async onLoad(options) {
    console.log('申请入驻页面加载');
    await this.loadInstitutionTypes();
  },

  // 加载机构分类
  async loadInstitutionTypes() {
    try {
      const result = await getInstitutionTypes();
      if (result.code === 200 && result.data) {
        this.setData({
          institutionTypes: result.data
        });
      }
    } catch (error) {
      console.error('加载机构分类失败:', error);
    }
  },

  // 输入框变化
  onInputChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value
    });
  },

  // 提交申请
  async onSubmit() {
    if (this.data.submitting) return;

    // 表单验证
    if (!this.validateForm()) {
      return;
    }

    try {
      this.setData({ submitting: true });

      // 调用申请入驻的API
      const result = await applyInstitution(this.data.formData);

      if (result.code === 200) {
        wx.showModal({
          title: '申请提交成功',
          content: '您的入驻申请已提交，我们将在3个工作日内审核并联系您。',
          showCancel: false,
          success: () => {
            wx.navigateBack();
          }
        });
      } else {
        wx.showToast({
          title: result.msg || '提交失败，请重试',
          icon: 'none'
        });
      }

    } catch (error) {
      console.error('提交申请失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 表单验证
  validateForm() {
    const { formData } = this.data;

    if (!formData.name.trim()) {
      wx.showToast({
        title: '请输入机构名称',
        icon: 'none'
      });
      return false;
    }

    if (!formData.typeId) {
      wx.showToast({
        title: '请选择机构分类',
        icon: 'none'
      });
      return false;
    }

    if (!formData.contactPerson.trim()) {
      wx.showToast({
        title: '请输入联系人',
        icon: 'none'
      });
      return false;
    }

    if (!formData.phone.trim()) {
      wx.showToast({
        title: '请输入联系电话',
        icon: 'none'
      });
      return false;
    }

    // 验证手机号格式
    const phoneRegex = /^1[3-9]\d{9}$/;
    if (!phoneRegex.test(formData.phone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      });
      return false;
    }

    if (!formData.licenseNo.trim()) {
      wx.showToast({
        title: '请输入营业执照号码',
        icon: 'none'
      });
      return false;
    }

    if (!formData.legalPerson.trim()) {
      wx.showToast({
        title: '请输入法人代表姓名',
        icon: 'none'
      });
      return false;
    }

    if (!formData.detailAddress.trim()) {
      wx.showToast({
        title: '请输入详细地址',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 选择机构分类
  onSelectType() {
    const { institutionTypes } = this.data;
    if (institutionTypes.length === 0) {
      wx.showToast({
        title: '暂无分类数据',
        icon: 'none'
      });
      return;
    }

    wx.showActionSheet({
      itemList: institutionTypes.map(type => type.name),
      success: (res) => {
        const selectedType = institutionTypes[res.tapIndex];
        this.setData({
          'formData.typeId': selectedType.id,
          selectedTypeName: selectedType.name
        });
      }
    });
  },

  // 上传营业执照
  onUploadLicense() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0];
        this.uploadImage(tempFilePath, 'licenseImage');
      }
    });
  },

  // 上传商家图片
  onUploadImages() {
    wx.chooseImage({
      count: 9,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePaths = res.tempFilePaths;
        this.uploadMultipleImages(tempFilePaths);
      }
    });
  },

  // 上传单张图片
  uploadImage(filePath, field) {
    wx.showLoading({ title: '上传中...' });

    wx.uploadFile({
      url: getApp().globalData.baseUrl + '/blade-resource/oss/endpoint/put-file',
      filePath: filePath,
      name: 'file',
      header: {
        'Blade-Auth': wx.getStorageSync('token')
      },
      success: (res) => {
        const data = JSON.parse(res.data);
        if (data.code === 200) {
          this.setData({
            [`formData.${field}`]: data.data.link
          });
          wx.showToast({
            title: '上传成功',
            icon: 'success'
          });
        } else {
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 上传多张图片
  async uploadMultipleImages(filePaths) {
    wx.showLoading({ title: '上传中...' });

    try {
      const uploadPromises = filePaths.map(filePath => {
        return new Promise((resolve, reject) => {
          wx.uploadFile({
            url: getApp().globalData.baseUrl + '/blade-resource/oss/endpoint/put-file',
            filePath: filePath,
            name: 'file',
            header: {
              'Blade-Auth': wx.getStorageSync('token')
            },
            success: (res) => {
              const data = JSON.parse(res.data);
              if (data.code === 200) {
                resolve(data.data.link);
              } else {
                reject(new Error('上传失败'));
              }
            },
            fail: reject
          });
        });
      });

      const imageUrls = await Promise.all(uploadPromises);
      const imagesJson = JSON.stringify(imageUrls.map((url, index) => ({
        url: url,
        type: 1,
        sort: index
      })));

      this.setData({
        'formData.images': imagesJson
      });

      wx.showToast({
        title: '上传成功',
        icon: 'success'
      });
    } catch (error) {
      wx.showToast({
        title: '上传失败',
        icon: 'none'
      });
    } finally {
      wx.hideLoading();
    }
  },

  // 跳转到合作申请页面
  onGoCooperation() {
    wx.navigateTo({
      url: '/pkg_user/pages/cooperation/cooperation'
    });
  }
});
