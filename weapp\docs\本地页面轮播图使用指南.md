# 本地页面轮播图使用指南

## 概述

本文档介绍如何在本地页面中使用轮播图功能，包括组件配置、数据结构、样式定制和事件处理。

## 功能特性

### ✨ 主要功能
- **自动轮播**：支持自动切换，可配置轮播间隔
- **手动滑动**：用户可以手动滑动切换轮播图
- **指示器**：显示当前轮播位置的指示点
- **点击事件**：支持轮播图点击跳转
- **文字覆盖**：支持在图片上显示标题和副标题
- **响应式高度**：可配置轮播图高度

### 🎯 设计特点
- **无缝循环**：支持无限循环轮播
- **平滑过渡**：流畅的切换动画效果
- **多格式支持**：兼容字符串和对象两种数据格式
- **默认数据**：提供默认轮播图，确保页面正常显示

## 快速开始

### 1. 组件引入

在页面的 JSON 配置文件中引入 banner-swiper 组件：

```json
{
  "usingComponents": {
    "banner-swiper": "/pages/index/components/banner-swiper/index"
  }
}
```

### 2. 页面使用

在 WXML 文件中使用轮播图组件：

```xml
<!-- 轮播图区域 -->
<banner-swiper 
  list="{{banners}}" 
  height="{{300}}"
  bind:bannertap="onBannerTap"
/>
```

### 3. 数据配置

在页面 JS 文件中配置轮播图数据：

```javascript
const { getBannersFromAPI } = require('../../stores/indexStore.js');

Page({
  data: {
    banners: []
  },

  async onLoad() {
    await this.loadBanners();
  },

  // 加载轮播图数据
  async loadBanners() {
    try {
      const banners = await getBannersFromAPI();
      const processedBanners = banners && banners.length > 0 ? banners : this.getDefaultBanners();
      this.setData({ banners: processedBanners });
    } catch (error) {
      console.error('加载轮播图失败:', error);
      this.setData({ banners: this.getDefaultBanners() });
    }
  },

  // 获取默认轮播图数据
  getDefaultBanners() {
    return [
      {
        id: 1,
        image: '/assets/images/banner.png',
        title: '发现身边好机构',
        subtitle: '找到最适合你的服务',
        link: '',
        type: 'default'
      }
    ];
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const { banner } = e.detail;
    if (banner.link) {
      wx.navigateTo({
        url: banner.link
      });
    }
  }
});
```

## 数据结构

### 轮播图数据格式

支持两种数据格式：

#### 1. 简单格式（字符串数组）
```javascript
banners: [
  '/assets/images/banner1.png',
  '/assets/images/banner2.png',
  '/assets/images/banner3.png'
]
```

#### 2. 完整格式（对象数组）
```javascript
banners: [
  {
    id: 1,                              // 唯一标识
    image: '/assets/images/banner1.png', // 图片路径
    title: '主标题',                     // 主标题（可选）
    subtitle: '副标题',                  // 副标题（可选）
    link: '/pages/detail/detail',       // 跳转链接（可选）
    type: 'default'                     // 类型标识（可选）
  }
]
```

### 组件属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| list | Array | [] | 轮播图数据数组 |
| height | Number | 350 | 轮播图高度（单位：rpx） |

### 组件事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| bannertap | 轮播图点击事件 | e.detail.banner - 被点击的轮播图数据 |

## 样式定制

### 基础样式

轮播图组件提供了以下样式类：

```css
/* 轮播图容器 */
.top-banner {
  width: 100%;
  margin-bottom: 30rpx;
  position: relative;
}

/* 轮播图项目 */
.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 轮播图图片 */
.banner-image {
  width: 100%;
  height: 100%;
}

/* 文字覆盖层 */
.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  padding: 40rpx 30rpx 30rpx 30rpx;
  color: #fff;
}
```

### 自定义样式

可以通过页面样式文件覆盖组件样式：

```css
/* 调整轮播图间距 */
.main-content > banner-swiper {
  margin-bottom: 30rpx;
}

/* 自定义指示器颜色 */
swiper {
  --indicator-color: rgba(255,255,255,0.5);
  --indicator-active-color: #fff;
}
```

## 最佳实践

### 1. 图片规格
- **推荐尺寸**：750x300 像素
- **格式**：JPG 或 PNG
- **大小**：建议小于 200KB

### 2. 数据加载
- 使用异步加载，避免阻塞页面渲染
- 提供默认数据，确保页面正常显示
- 添加错误处理，提升用户体验

### 3. 性能优化
- 图片懒加载
- 合理设置轮播间隔
- 避免过多轮播图影响性能

### 4. 用户体验
- 提供清晰的视觉反馈
- 确保点击区域足够大
- 添加加载状态提示

## 注意事项

1. **数据兼容性**：组件同时支持字符串和对象格式，确保向后兼容
2. **图片加载**：确保图片路径正确，添加加载失败处理
3. **点击事件**：合理处理点击事件，避免误触
4. **性能考虑**：轮播图数量不宜过多，建议 3-5 张

## 故障排除

### 常见问题

**Q: 轮播图不显示？**
A: 检查数据格式和图片路径，确保组件正确引入。

**Q: 点击事件不响应？**
A: 检查事件绑定和数据传递，确保 banner 数据包含 link 字段。

**Q: 样式显示异常？**
A: 检查 height 属性设置，确保容器有足够空间。

### 调试技巧

1. 在控制台查看数据加载情况
2. 检查组件属性传递是否正确
3. 验证图片资源是否存在
4. 测试不同数据格式的兼容性

## 更新日志

### v1.0.0 (2025-07-31)
- 初始版本发布
- 支持基础轮播功能
- 添加文字覆盖层
- 支持点击事件
- 兼容多种数据格式
