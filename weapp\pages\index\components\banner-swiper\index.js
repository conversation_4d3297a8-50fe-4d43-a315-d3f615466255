Component({
  properties: {
    list: {
      type: Array,
      value: []
    },
    height: {
      type: Number,
      value: 350 // 默认高度 350rpx
    }
  },
  data: {},
  methods: {
    // 轮播图点击事件
    onBannerTap(e) {
      const banner = e.currentTarget.dataset.banner;
      console.log('Banner clicked:', banner);

      // 向父组件传递点击事件
      this.triggerEvent('bannertap', { banner });
    }
  }
});
