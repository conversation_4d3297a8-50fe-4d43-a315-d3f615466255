<!-- pkg_user/pages/cooperation/cooperation.wxml -->
<!-- 顶部介绍 -->
<view class="intro-section">
  <!-- 装饰元素 -->
  <view class="intro-decoration">
    <view class="decoration-circle circle-1"></view>
    <view class="decoration-circle circle-2"></view>
    <view class="decoration-circle circle-3"></view>
  </view>

  <view class="intro-header">
    <view class="intro-icon">🤝</view>
    <text class="intro-title">合作共赢</text>
    <text class="intro-subtitle">携手共创美好未来</text>
  </view>
  <view class="intro-content">
    <text class="intro-text">我们诚邀各界合作伙伴加入，共同打造优质的本地服务生态圈</text>

    <!-- 特色亮点 -->
    <view class="intro-highlights">
      <view class="highlight-item">
        <text class="highlight-icon">💼</text>
        <text class="highlight-text">专业团队</text>
      </view>
      <view class="highlight-item">
        <text class="highlight-icon">🎯</text>
        <text class="highlight-text">精准定位</text>
      </view>
      <view class="highlight-item">
        <text class="highlight-icon">📈</text>
        <text class="highlight-text">共同成长</text>
      </view>
    </view>
  </view>
</view>

<scroll-view scroll-y class="form-scroll">
  <!-- 合作类型选择 -->
  <view class="form-section">
    <text class="section-title">合作类型</text>
    
    <view class="cooperation-types">
      <view 
        class="type-item {{formData.cooperationType === item.id ? 'active' : ''}}"
        wx:for="{{cooperationTypes}}" 
        wx:key="id"
        data-id="{{item.id}}"
        bindtap="onCooperationTypeChange">
        <view class="type-header">
          <text class="type-name">{{item.name}}</text>
          <view class="type-check {{formData.cooperationType === item.id ? 'checked' : ''}}">
            <text class="check-icon">✓</text>
          </view>
        </view>
        <text class="type-desc">{{item.desc}}</text>
      </view>
    </view>

    <!-- 选中类型的详细信息 -->
    <view class="selected-type-info" wx:if="{{selectedTypeInfo}}">
      <view class="info-header">
        <text class="info-title">{{selectedTypeInfo.name}}</text>
      </view>
      <text class="info-desc">{{selectedTypeInfo.desc}}</text>
      <view class="info-actions">
        <view class="action-btn secondary" bindtap="onViewPolicy">
          <text class="action-text">查看政策</text>
        </view>
        <view class="action-btn secondary" bindtap="onContactService">
          <text class="action-text">咨询客服</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 联系信息 -->
  <view class="form-section">
    <text class="section-title">联系信息</text>
    
    <view class="form-item">
      <text class="form-label">联系人姓名 *</text>
      <input 
        class="form-input" 
        placeholder="请输入您的姓名"
        value="{{formData.name}}"
        data-field="name"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">联系电话 *</text>
      <input 
        class="form-input" 
        placeholder="请输入手机号"
        type="number"
        value="{{formData.phone}}"
        data-field="phone"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">微信号</text>
      <view class="input-with-btn">
        <input 
          class="form-input flex-input" 
          placeholder="请输入微信号"
          value="{{formData.wechat}}"
          data-field="wechat"
          bindinput="onInputChange"
        />
        <view class="input-btn" bindtap="onGetWechatInfo">
          <text class="btn-text">获取</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 合作详情 -->
  <view class="form-section">
    <text class="section-title">合作详情</text>
    
    <view class="form-item">
      <text class="form-label">详细描述 *</text>
      <textarea 
        class="form-textarea" 
        placeholder="请详细描述您的合作意向、资源优势、预期目标等"
        value="{{formData.cooperationDetails}}"
        data-field="cooperationDetails"
        bindinput="onInputChange"
        maxlength="500"
      />
      <view class="textarea-counter">
        <text class="counter-text">{{formData.cooperationDetails.length}}/500</text>
      </view>
    </view>
  </view>

  <!-- 温馨提示 -->
  <view class="tips-section">
    <view class="tips-header">
      <text class="tips-icon">💡</text>
      <text class="tips-title">温馨提示</text>
    </view>
    <view class="tips-content">
      <text class="tips-text">• 我们将在3个工作日内联系您</text>
      <text class="tips-text">• 请确保联系方式准确无误</text>
      <text class="tips-text">• 详细的合作描述有助于我们更好地了解您的需求</text>
      <text class="tips-text">• 如有疑问，可随时联系客服咨询</text>
    </view>
  </view>
</scroll-view>

<!-- 底部提交按钮 -->
<view class="submit-section">
  <button 
    class="submit-btn {{submitting ? 'submitting' : ''}}" 
    bindtap="onSubmit"
    disabled="{{submitting}}"
  >
    {{submitting ? '提交中...' : '提交申请'}}
  </button>
  
  <view class="submit-tips">
    <text class="submit-tip-text">提交即表示您同意我们的合作条款</text>
  </view>
</view>
