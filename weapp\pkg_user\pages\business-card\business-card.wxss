/* pkg_user/pages/business-card/business-card.wxss */

.card-container {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部搜索栏 */
.search-header {
  background: #fff;
  padding: 20rpx;
  display: flex;
  align-items: center;
  gap: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f8f8f8;
  border-radius: 25rpx;
  padding: 0 20rpx;
}

.search-input {
  flex: 1;
  height: 70rpx;
  font-size: 28rpx;
  color: #333;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin-left: 10rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-left: 12rpx;
  flex-shrink: 0; /* 防止图标缩小 */
}
.add-card-btn {
  width: 70rpx;
  height: 70rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.add-icon {
  color: #fff;
  font-size: 36rpx;
  font-weight: 300;
}

/* 分类筛选 */
.category-filter {
  background: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  flex-shrink: 0;
  padding: 12rpx 24rpx;
  margin-right: 20rpx;
  background: #f8f8f8;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff6b6b;
  color: #fff;
  transform: scale(1.05);
}

/* 名片列表 */
.card-list {
  padding: 20rpx;
}

.card-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.card-item:active {
  transform: scale(0.98);
}

/* 名片头像 */
.card-avatar {
  width: 100rpx;
  height: 100rpx;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3rpx solid #f0f0f0;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 50%;
}

/* 名片信息 */
.card-info {
  flex: 1;
  min-width: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
}

.card-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.card-category {
  padding: 4rpx 12rpx;
  background: #e3f2fd;
  color: #1976d2;
  font-size: 22rpx;
  border-radius: 12rpx;
}

.card-details {
  margin-bottom: 16rpx;
}

.card-position {
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
}

.card-company {
  font-size: 26rpx;
  color: #999;
}

.card-contact {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.contact-phone,
.contact-email {
  font-size: 24rpx;
  color: #888;
  display: flex;
  align-items: center;
}

/* 名片操作 */
.card-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-between;
  height: 100rpx;
  margin-left: 20rpx;
}

.action-time {
  font-size: 22rpx;
  color: #bbb;
}

.action-arrow {
  font-size: 32rpx;
  color: #ddd;
  font-weight: 300;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
  margin-bottom: 0;
  margin-right: 12rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
  opacity: 0.5;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 12rpx;
}

.empty-tip {
  font-size: 26rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.empty-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 28rpx;
  border-radius: 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #666;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

.no-more {
  display: flex;
  justify-content: center;
  padding: 40rpx 0;
  font-size: 26rpx;
  color: #999;
}
