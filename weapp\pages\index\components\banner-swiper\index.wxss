.top-banner {
  width: 100%;
  margin-bottom: 30rpx;
  position: relative;
}

.top-banner::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(to bottom, rgba(255, 133, 133, 0.6), transparent);
  pointer-events: none;
  z-index: 1;
}

.top-banner::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(to top, rgba(245, 245, 245, 0.9), transparent);
  pointer-events: none;
  z-index: 1;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 文字覆盖层 */
.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
  padding: 40rpx 30rpx 30rpx 30rpx;
  color: #fff;
}

.banner-content {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.banner-title {
  font-size: 32rpx;
  font-weight: 600;
  line-height: 1.3;
  text-shadow: 0 2rpx 4rpx rgba(0,0,0,0.3);
}

.banner-subtitle {
  font-size: 26rpx;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1rpx 2rpx rgba(0,0,0,0.3);
}
