// pkg_user/pages/business-card/business-card.js
const { request } = require('../../../utils/request');

Page({
  data: {
    // 名片列表
    cardList: [],
    // 加载状态
    loading: true,
    // 是否还有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 搜索关键词
    searchKeyword: '',
    // 分类筛选
    selectedCategory: 'all',
    categories: [
      { id: 'all', name: '全部' },
      { id: 'business', name: '商务' },
      { id: 'personal', name: '个人' },
      { id: 'friend', name: '朋友' },
      { id: 'colleague', name: '同事' },
      { id: 'client', name: '客户' }
    ]
  },

  onLoad(options) {
    console.log('名片页面加载');
    this.loadCardList();
  },

  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },

  // 加载名片列表
  async loadCardList(isRefresh = false) {
    if (this.data.loading && !isRefresh) return;

    try {
      this.setData({ loading: true });

      const params = {
        page: isRefresh ? 1 : this.data.currentPage,
        pageSize: this.data.pageSize,
        category: this.data.selectedCategory === 'all' ? '' : this.data.selectedCategory,
        keyword: this.data.searchKeyword
      };

      // 模拟API调用
      const result = await this.mockApiCall(params);

      if (result.code === 200 && result.data) {
        const newList = result.data.records || [];
        const cardList = isRefresh ? newList : [...this.data.cardList, ...newList];
        
        this.setData({
          cardList: cardList,
          hasMore: newList.length >= this.data.pageSize,
          currentPage: isRefresh ? 2 : this.data.currentPage + 1,
          loading: false
        });

        console.log('名片列表加载完成:', cardList.length);
      } else {
        throw new Error(result.msg || '加载失败');
      }
    } catch (error) {
      console.error('加载名片列表失败:', error);
      this.setData({ loading: false });
      
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  // 模拟API调用
  mockApiCall(params) {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockData = this.generateMockData(params.page, params.pageSize);
        resolve({
          code: 200,
          data: {
            records: mockData
          }
        });
      }, 500);
    });
  },

  // 生成模拟数据
  generateMockData(page, pageSize) {
    const mockCards = [];
    const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十'];
    const companies = ['阿里巴巴', '腾讯科技', '百度公司', '字节跳动', '美团', '滴滴出行', '京东集团', '网易'];
    const positions = ['产品经理', '技术总监', '销售经理', '市场专员', '设计师', '运营总监', '财务经理', '人事主管'];
    
    for (let i = 0; i < pageSize && (page - 1) * pageSize + i < 20; i++) {
      const index = (page - 1) * pageSize + i;
      mockCards.push({
        id: `card_${index + 1}`,
        name: names[index % names.length],
        company: companies[index % companies.length],
        position: positions[index % positions.length],
        phone: `138${String(index).padStart(8, '0')}`,
        email: `user${index + 1}@example.com`,
        address: `北京市朝阳区某某街道${index + 1}号`,
        avatar: `/assets/images/avatar/avatar${(index % 8) + 1}.png`,
        category: this.data.categories[(index % 5) + 1].id,
        createTime: new Date(Date.now() - index * 24 * 60 * 60 * 1000).toLocaleDateString(),
        remark: `这是${names[index % names.length]}的名片备注信息`
      });
    }
    
    return mockCards;
  },

  // 刷新数据
  async refreshData() {
    await this.loadCardList(true);
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore || this.data.loading) return;
    await this.loadCardList();
  },

  // 搜索
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.setData({
      currentPage: 1,
      cardList: []
    });
    this.loadCardList(true);
  },

  // 分类筛选
  onCategoryChange(e) {
    const categoryId = e.currentTarget.dataset.id;
    this.setData({
      selectedCategory: categoryId,
      currentPage: 1,
      cardList: []
    });
    this.loadCardList(true);
  },

  // 点击名片项
  onCardTap(e) {
    const card = e.currentTarget.dataset.item;
    console.log('点击名片:', card);
    
    // 跳转到名片详情页
    wx.navigateTo({
      url: `/pkg_user/pages/card-detail/card-detail?id=${card.id}`
    });
  },

  // 添加名片
  onAddCard() {
    wx.navigateTo({
      url: '/pkg_user/pages/add-card/add-card'
    });
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.refreshData().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore();
  }
});
