<layout title="我的帖子"  showBack="true" showBackButton="true" background="#fff"
  text-color="black">
  <view class="my-posts-tabs">
    <view class="tab-item {{tab==='all' ? 'active' : ''}}" data-tab="all" bindtap="onTabChange">全部</view>
    <view class="tab-item {{tab==='published' ? 'active' : ''}}" data-tab="published" bindtap="onTabChange">已发布</view>
    <view class="tab-item {{tab==='draft' ? 'active' : ''}}" data-tab="draft" bindtap="onTabChange">草稿箱</view>
  </view>
  <view class="main-content theme-bg">
    <scroll-view
      scroll-y
      class="post-list-scroll"
      style="min-height: 100vh;"
      bindscrolltolower="onReachBottom"
    >
      <block wx:for="{{posts}}" wx:key="id">
        <post-item post="{{item}}" bind:edit="onEditPost" bind:delete="onDeletePost" bind:moveDraft="onMoveDraft" bind:completed="onCompletedPost" bind:more="onMore" />
      </block>
      <view wx:if="{{loading}}" class="loading-text">加载中...</view>
      <view wx:if="{{noMore}}" class="no-more-text" style="padding-bottom: 40rpx;">没有更多了</view>
    </scroll-view>
  </view>
  <!-- 遮罩层 -->
  <view class="action-mask" wx:if="{{showActionMenu}}" bindtap="hideActionMenu"></view>
  <!-- 底部弹出菜单 -->
  <view class="{{showActionMenu ? 'show action-sheet' : 'hidden'}}">
    <view class="action-sheet-inner">
      <view class="action-btn delete" bindtap="onDeletePostMenu">删除</view>
      <view class="action-btn edit" bindtap="onEditPostMenu">编辑</view>
      <view class="action-btn move" bindtap="onMoveDraftMenu">移动草稿箱</view>
      <view class="action-btn complete" bindtap="onCompletedPostMenu">标记已完成</view>
    </view>
    <view class="action-btn cancel" bindtap="hideActionMenu">取消</view>
  </view>
</layout> 