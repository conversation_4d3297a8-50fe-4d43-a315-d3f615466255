# API接口使用规范指南

## 概述

本文档规范了小程序中API接口的统一使用方式，确保代码的一致性和可维护性。

## 核心原则

### 1. 统一配置管理
- 所有API接口地址统一在 `weapp/config/api.js` 中定义
- 按功能模块分组管理接口
- 避免在代码中硬编码接口地址

### 2. Store模式
- 所有API调用必须封装在 `weapp/stores/` 目录下
- 每个功能模块对应一个store文件
- Store负责数据处理、错误处理和格式转换

### 3. 标准化请求格式
- 统一使用 `weapp/utils/request.js` 进行网络请求
- 请求方法名使用大写 (`GET`, `POST`, `PUT`, `DELETE`)
- 参数传递遵循RESTful规范

## 目录结构

```
weapp/
├── config/
│   └── api.js                 # API接口配置文件
├── stores/
│   ├── institutionStore.js    # 机构相关API
│   ├── institutionTypeStore.js # 机构分类API
│   ├── postStore.js           # 帖子相关API
│   └── userStore.js           # 用户相关API
├── utils/
│   └── request.js             # 网络请求工具
└── docs/
    └── API_USAGE_GUIDE.md     # 本文档
```

## API配置规范

### 配置文件结构 (`config/api.js`)

```javascript
const config = {
  // 基础配置
  baseUrl: 'http://localhost',
  
  // 机构相关接口
  institution: {
    page: '/blade-chat/institution/page',        // 分页查询
    detail: '/blade-chat/institution/detail',    // 获取详情
    list: '/blade-chat/institution/list',        // 简单列表
    myInstitutions: '/blade-chat/institution/my-institution', // 我的机构
    create: '/blade-chat/institution/create',    // 创建
    update: '/blade-chat/institution/update',    // 更新
    delete: '/blade-chat/institution/delete',    // 删除
  },
  
  // 机构分类接口
  institutionType: {
    list: '/blade-ad/institutiontype/list',      // 分类列表
    page: '/blade-ad/institutiontype/page',      // 分页查询
    detail: '/blade-ad/institutiontype/detail',  // 分类详情
  }
};

module.exports = config;
```

### 命名规范

1. **模块名**: 使用驼峰命名法，如 `institution`, `institutionType`
2. **接口名**: 使用驼峰命名法，语义化命名
   - `list` - 获取列表
   - `page` - 分页查询
   - `detail` - 获取详情
   - `create` - 创建
   - `update` - 更新
   - `delete` - 删除

## Store使用规范

### Store文件结构

```javascript
// stores/institutionStore.js
const { request } = require('../utils/request.js');
const { institution: API } = require('../config/api.js');

/**
 * 获取机构列表
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 机构列表数据
 */
const getInstitutionList = async (params = {}) => {
  try {
    const response = await request({
      url: API.page,
      method: 'GET',
      data: {
        current: params.current || 1,
        size: params.size || 10,
        ...params
      }
    });

    if (response.code === 200) {
      return processInstitutions(response.data);
    } else {
      throw new Error(response.msg || '获取机构列表失败');
    }
  } catch (error) {
    console.error('获取机构列表失败:', error);
    return getDefaultInstitutions();
  }
};

// 数据处理函数
const processInstitutions = (data) => {
  // 数据转换逻辑
  return {
    records: data.records || [],
    total: data.total || 0,
    current: data.current || 1,
    size: data.size || 10
  };
};

// 默认数据
const getDefaultInstitutions = () => {
  return {
    records: [],
    total: 0,
    current: 1,
    size: 10
  };
};

module.exports = {
  getInstitutionList,
  // 其他导出函数...
};
```

### Store规范要点

1. **引入API配置**: 使用解构赋值引入对应模块的API配置
2. **函数命名**: 使用动词+名词的形式，如 `getInstitutionList`
3. **参数处理**: 提供默认参数，支持参数扩展
4. **错误处理**: 统一的try-catch处理，提供默认数据回退
5. **数据处理**: 将后端数据转换为前端友好格式
6. **文档注释**: 使用JSDoc格式注释函数参数和返回值

## 页面中的使用方式

### 引入Store

```javascript
// pages/local/local.js
const { getInstitutionList } = require('../../../stores/institutionStore.js');
const { getInstitutionTypeList } = require('../../../stores/institutionTypeStore.js');
```

### 调用API

```javascript
// 加载机构列表
async loadInstitutionList(isRefresh = false) {
  try {
    const params = {
      current: isRefresh ? 1 : this.data.currentPage,
      size: this.data.pageSize,
      name: this.data.searchKeyword,
      typeId: this.data.selectedCategory === 'all' ? null : this.data.selectedCategory
    };
    
    const result = await getInstitutionList(params);
    
    this.setData({
      institutionList: isRefresh ? result.records : [...this.data.institutionList, ...result.records],
      hasMore: result.current < result.pages,
      currentPage: result.current + 1
    });
  } catch (error) {
    wx.showToast({
      title: '加载失败',
      icon: 'none'
    });
  }
}
```

## 错误处理规范

### 1. Store层错误处理

```javascript
const getInstitutionList = async (params = {}) => {
  try {
    const response = await request({
      url: API.page,
      method: 'GET',
      data: params
    });

    if (response.code === 200) {
      return processData(response.data);
    } else {
      console.error('API返回错误:', response.msg);
      return getDefaultData();
    }
  } catch (error) {
    console.error('网络请求失败:', error);
    return getDefaultData();
  }
};
```

### 2. 页面层错误处理

```javascript
async loadData() {
  try {
    const result = await getInstitutionList(params);
    // 处理成功结果
  } catch (error) {
    // 用户友好的错误提示
    wx.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
  }
}
```

## 最佳实践

### 1. 接口版本管理
- 在API配置中预留版本字段
- 支持多版本接口并存

### 2. 缓存策略
- 对不经常变化的数据实现缓存
- 提供缓存刷新机制

### 3. 请求优化
- 实现请求去重
- 支持请求取消
- 合理设置超时时间

### 4. 数据格式统一
- 统一分页数据格式
- 统一错误响应格式
- 统一时间格式处理

## 迁移指南

### 从硬编码到配置化

**迁移前:**
```javascript
const response = await request({
  url: '/blade-chat/institution/page',
  method: 'GET'
});
```

**迁移后:**
```javascript
const { institution: API } = require('../config/api.js');

const response = await request({
  url: API.page,
  method: 'GET'
});
```

### 从页面直调到Store模式

**迁移前:**
```javascript
// 在页面中直接调用API
async loadData() {
  const response = await request({
    url: '/blade-chat/institution/page',
    method: 'GET'
  });
  // 处理数据...
}
```

**迁移后:**
```javascript
// 在Store中封装
const getInstitutionList = async (params) => {
  // API调用和数据处理逻辑
};

// 在页面中使用
const { getInstitutionList } = require('../stores/institutionStore.js');

async loadData() {
  const result = await getInstitutionList(params);
  // 使用处理好的数据
}
```

## 总结

通过统一的API配置管理和Store模式，我们实现了：

1. **代码复用**: 避免重复的API调用代码
2. **统一管理**: 集中管理所有接口地址
3. **易于维护**: 接口变更只需修改配置文件
4. **错误处理**: 统一的错误处理和用户反馈
5. **数据处理**: 标准化的数据格式转换

遵循这些规范，可以确保项目的可维护性和代码质量。
