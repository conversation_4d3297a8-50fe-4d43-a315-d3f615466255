<layout
  title="我的收藏"
  showSearch="{{false}}"
  showLocation="{{false}}"
  background="linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%)"
  showBack="{{true}}"
>
</layout>
<!--我的收藏页面-->
<view class="collections-page">
  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/assets/images/common/search.png" mode="aspectFit"/>
      <input
        class="search-input"
        placeholder="搜索我的收藏内容"
        placeholder-class="input-placeholder"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        focus="{{searchFocus}}"
      />
      <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-item {{selectedFilter === '' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="">
        全部
      </view>
      <view class="filter-item {{selectedFilter === 'recent' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="recent">
        最新
      </view>
      <view wx:if="{{tags.length > 0}}" wx:for="{{tags}}" wx:key="*this"
            class="filter-item {{selectedFilter === item ? 'active' : ''}}"
            bindtap="onFilterTap"
            data-filter="{{item}}">
        {{item}}
      </view>
    </scroll-view>
    <view class="filter-more" bindtap="onShowMoreFilter">
      <image class="filter-icon" src="/assets/images/common/filter-color-main.png" mode="aspectFit"/>
      <text class="filter-text">清空</text>
    </view>
  </view>

  <!-- 收藏列表 -->
  <scroll-view
    class="card-list-scroll"
    scroll-y="true"
    bindscrolltolower="onReachBottom"
    refresher-enabled="true"
    bindrefresherrefresh="onRefresh"
    refresher-triggered="{{refreshing}}"
  >

    <!-- 加载状态 -->
    <view wx:if="{{loading && posts.length === 0}}" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 收藏内容列表 -->
    <view wx:elif="{{posts.length > 0}}" class="card-list">
      <view wx:for="{{posts}}" wx:key="id" class="card-item" bindtap="onPostTap" data-id="{{item.id}}">
        <!-- 帖子头部 -->
        <view class="card-header">
          <view class="card-avatar">
            <image
              class="avatar-image"
              src="{{item.images && item.images.length > 0 ? item.images[0] : '/assets/images/common/default-post.png'}}"
              mode="aspectFill"
            />
          </view>
          <view class="card-info">
            <view class="card-name">{{item.title || '未填写'}}</view>
            <view class="card-position">{{item.categoryName || '其他'}}</view>
            <view class="card-company">{{item.authorName || '匿名用户'}}</view>
          </view>
          <view class="card-actions">
            <view class="action-btn like-btn"
                  bindtap="onRemoveFavorite"
                  data-id="{{item.id}}"
                  data-index="{{index}}">
              <text class="action-icon">🗑️</text>
              <text class="action-count">删除</text>
            </view>
          </view>
        </view>

        <!-- 帖子内容 -->
        <view class="card-content">
          <view wx:if="{{item.content}}" class="card-profile">
            <text class="profile-text">{{item.content}}</text>
          </view>

          <view class="card-contact">
            <view class="contact-item">
              <image class="contact-icon" src="/assets/images/common/time.png" mode="aspectFit"/>
              <text class="contact-text">{{item.createTime}}</text>
            </view>
            <view class="contact-item">
              <image class="contact-icon" src="/assets/images/common/view.png" mode="aspectFit"/>
              <text class="contact-text">浏览 {{item.viewCount || 0}}</text>
            </view>
          </view>
        </view>

        <!-- 帖子底部 -->
        <view class="card-footer">
          <view class="card-stats">
            <text class="stat-item">收藏时间</text>
            <text class="stat-time">{{item.favoriteTime || item.createTime}}</text>
          </view>
          <view class="card-operations">
            <button class="operation-btn favorite-btn active"
                    bindtap="onShareTap"
                    data-id="{{item.id}}"
                    data-index="{{index}}">
              <text class="btn-icon">📤</text>
              <text class="btn-text">分享</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <image class="empty-icon" src="/assets/images/mine/collections.png" mode="aspectFit"/>
      <text class="empty-title">暂无收藏内容</text>
      <text class="empty-desc">浏览时点击收藏，内容会出现在这里</text>
      <view class="empty-action" bindtap="onGoExplore">
        <text class="action-text">去逛逛</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{posts.length > 0 && !noMore}}" class="load-more">
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <text wx:else class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{noMore && posts.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多了</text>
    </view>
  </scroll-view>

  <!-- 筛选弹窗 -->
  <view wx:if="{{showFilterModal}}" class="modal-overlay" bindtap="onHideFilterModal">
    <view class="filter-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">收藏管理</text>
        <view class="modal-close" bindtap="onHideFilterModal">×</view>
      </view>

      <view class="modal-content">
        <view class="filter-section">
          <text class="section-title">收藏分类</text>
          <view class="range-options">
            <view class="range-item" bindtap="onClearAll">
              清空所有收藏
            </view>
            <view class="range-item" bindtap="onExportCollections">
              导出收藏列表
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn reset-btn" bindtap="onHideFilterModal">取消</button>
        <button class="modal-btn confirm-btn" bindtap="onHideFilterModal">确定</button>
      </view>
    </view>
  </view>
</view>