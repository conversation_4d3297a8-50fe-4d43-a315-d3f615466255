<!--我的收藏页面-->
<view class="collections-page">
  <!-- 页面头部 -->
  <view class="page-header">
    <view class="header-back" bindtap="onBack">
      <image class="back-icon" src="/assets/images/common/back.png" mode="aspectFit"/>
    </view>
    <view class="header-title">我的收藏</view>
    <view class="header-action" bindtap="onClearAll">
      <text class="clear-text">清空</text>
    </view>
  </view>

  <!-- 标签筛选 -->
  <view class="tags-filter">
    <scroll-view class="tags-scroll" scroll-x="true">
      <view class="tag-item {{selectedTag === '' ? 'active' : ''}}" bindtap="onTagFilter" data-tag="">
        全部
      </view>
      <view wx:for="{{tags}}" wx:key="*this"
            class="tag-item {{selectedTag === item ? 'active' : ''}}"
            bindtap="onTagFilter"
            data-tag="{{item}}">
        {{item}}
      </view>
    </scroll-view>
  </view>

  <!-- 收藏列表 -->
  <scroll-view class="collections-scroll"
               scroll-y="true"
               bindscrolltolower="onReachBottom"
               refresher-enabled="true"
               bindrefresherrefresh="onRefresh"
               refresher-triggered="{{refreshing}}">

    <!-- 加载状态 -->
    <view wx:if="{{loading && posts.length === 0}}" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 收藏内容列表 -->
    <view wx:elif="{{posts.length > 0}}" class="posts-list">
      <view wx:for="{{posts}}" wx:key="id" class="post-item" bindtap="onPostTap" data-id="{{item.id}}">
        <!-- 帖子图片 -->
        <view class="post-image-container">
          <image class="post-image"
                 src="{{item.images && item.images.length > 0 ? item.images[0] : '/assets/images/common/default-post.png'}}"
                 mode="aspectFill"/>
          <view class="post-category">{{item.categoryName || '其他'}}</view>
        </view>

        <!-- 帖子信息 -->
        <view class="post-info">
          <view class="post-title">{{item.title}}</view>
          <view class="post-desc">{{item.content}}</view>
          <view class="post-meta">
            <view class="meta-item">
              <image class="meta-icon" src="/assets/images/common/time.png" mode="aspectFit"/>
              <text class="meta-text">{{item.createTime}}</text>
            </view>
            <view class="meta-item">
              <image class="meta-icon" src="/assets/images/common/view.png" mode="aspectFit"/>
              <text class="meta-text">{{item.viewCount || 0}}</text>
            </view>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="post-actions">
          <view class="action-btn" bindtap="onRemoveFavorite" data-id="{{item.id}}" data-index="{{index}}">
            <image class="action-icon" src="/assets/images/common/delete.png" mode="aspectFit"/>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <image class="empty-icon" src="/assets/images/mine/collections.png" mode="aspectFit"/>
      <text class="empty-title">暂无收藏内容</text>
      <text class="empty-desc">浏览时点击收藏，内容会出现在这里</text>
      <view class="empty-action" bindtap="onGoExplore">
        <text class="action-text">去逛逛</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{posts.length > 0 && !noMore}}" class="load-more">
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <text wx:else class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{noMore && posts.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多了</text>
    </view>
  </scroll-view>
</view>