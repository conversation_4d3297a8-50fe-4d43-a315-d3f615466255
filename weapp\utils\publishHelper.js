/**
 * 发布相关的工具函数
 */
class PublishHelper {
  /**
   * 构建发布数据
   * @param {Object} formData 表单数据
   * @returns {Object} 格式化后的发布数据
   */
  static buildPostData(formData) {
    // 处理图片数据 - 转换为字符串格式
    const processedImages = this.processImages(formData.images || []);
    const imagesString = processedImages.map(img => img.url).join(',');
    
    // 处理标签数据 - 转换为字符串格式
    const tagsString = (formData.selectedTags || []).join(',');
    
    
    return {
      // 接口要求的字段
      images: imagesString,
      tags: tagsString,
      contactName: formData.contactName || '',
      contactType: formData.contactType || 'phone',
      contactNumber: formData.contactNumber || '',
      content: formData.description || '', // 内容描述
      categoryId: parseInt(formData.categoryId) || 0,
      location: formData.location || '',
      address: formData.address || '',
      longitude: parseFloat(formData.longitude) || 0,
      latitude: parseFloat(formData.latitude) || 0,
      geolocation: formData.publishLocation || '',
      // 保留原有字段用于内部处理
      title: formData.title || '',
      description: formData.description || '',
      category: formData.category || '',
      selectedTags: formData.selectedTags || [],
      imageCount: formData.images ? formData.images.length : 0,
      tagCount: formData.selectedTags ? formData.selectedTags.length : 0,
      publishTime: new Date().toISOString(),
      publishType: formData.publishTypeActive || '普通',
      status: 'pending',
      userId: formData.userId || '',
      userName: formData.userName || '',
      platform: 'weapp',
      version: '1.0.0',
      deviceInfo: formData.deviceInfo || '',
      extra: {
        draftId: formData.draftId || null,
        source: 'publish_page',
        timestamp: Date.now()
      }
    };
  }

  /**
   * 构建草稿数据
   * @param {Object} formData 表单数据
   * @returns {Object} 格式化后的草稿数据
   */
  static buildDraftData(formData) {
    return {
      title: formData.title || '',
      description: formData.description || '',
      contactName: formData.contactName || '',
      contactType: formData.contactType || 'phone',
      contactNumber: formData.contactNumber || '',
      location: formData.location || '',
      latitude: formData.latitude || '',
      longitude: formData.longitude || '',
      tags: JSON.stringify(formData.selectedTags || []),
      images: JSON.stringify(formData.images || []),
      category: formData.category || '',
      saveTime: new Date().toISOString()
    };
  }

  /**
   * 验证表单数据
   * @param {Object} formData 表单数据
   * @returns {Object} 验证结果
   */
  static validateForm(formData) {
    const errors = [];

    // 验证必填字段
    if (!formData.description || !formData.description.trim()) {
      errors.push('请输入内容描述');
    }

    if (!formData.contactName || !formData.contactName.trim()) {
      errors.push('请输入联系人姓名');
    }

    if (!formData.contactNumber || !formData.contactNumber.trim()) {
      errors.push('请输入联系方式');
    }

    if (!formData.category && !formData.categoryId) {
      errors.push('请选择分类');
    }

    // 验证联系方式格式
    if (formData.contactType === 'phone' && formData.contactNumber) {
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(formData.contactNumber)) {
        errors.push('请输入正确的手机号码');
      }
    }

    if (formData.contactType === 'wechat' && formData.contactNumber) {
      if (formData.contactNumber.length < 6) {
        errors.push('微信号至少6位字符');
      }
    }

    // 验证内容长度
    if (formData.description && formData.description.length > 800) {
      errors.push('内容描述不能超过800字');
    }

    if (formData.title && formData.title.length > 50) {
      errors.push('标题不能超过50字');
    }

    // 验证图片数量
    if (formData.images && formData.images.length > 6) {
      errors.push('最多上传6张图片');
    }

    // 验证标签数量
    if (formData.selectedTags && formData.selectedTags.length > 5) {
      errors.push('最多选择5个标签');
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 处理图片数据
   * @param {Array} images 原始图片数组
   * @returns {Array} 处理后的图片数组
   */
  static processImages(images) {
    if (!images || images.length === 0) {
      return [];
    }

    return images.map((img, index) => {
      // 如果是已上传的图片（有fileId和url）
      if (img.fileId && img.url) {
        return {
          url: img.url,
          fileId: img.fileId,
          size: img.size || 0,
          type: img.type || 'image/jpeg',
          name: img.name || `image_${index + 1}.jpg`,
          isLocal: false
        };
      }
      
      // 如果是本地临时图片
      return {
        url: img.path || img.url,
        size: img.size || 0,
        type: img.type || 'image/jpeg',
        name: img.name || `image_${index + 1}.jpg`,
        isLocal: true
      };
    });
  }

  /**
   * 格式化标签数据
   * @param {Array} tags 标签数组
   * @returns {Array} 格式化后的标签数组
   */
  static formatTags(tags) {
    if (!tags || !Array.isArray(tags)) {
      return [];
    }

    return tags.filter(tag => tag && tag.trim()).map(tag => tag.trim());
  }

  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  static getDeviceInfo() {
    try {
      const deviceInfo = wx.getDeviceInfo();
      const windowInfo = wx.getWindowInfo();
      const appBaseInfo = wx.getAppBaseInfo();
      return {
        platform: deviceInfo.platform,
        system: deviceInfo.system,
        version: appBaseInfo.version,
        model: deviceInfo.model,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight
      };
    } catch (error) {
      console.error('获取设备信息失败:', error);
      return {};
    }
  }

  /**
   * 生成唯一ID
   * @returns {string} 唯一ID
   */
  static generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 格式化文件大小
   * @param {number} bytes 字节数
   * @returns {string} 格式化后的大小
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 检查网络状态
   * @returns {Promise} 网络状态
   */
  static async checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          resolve({
            isConnected: res.networkType !== 'none',
            networkType: res.networkType
          });
        },
        fail: () => {
          resolve({
            isConnected: false,
            networkType: 'unknown'
          });
        }
      });
    });
  }
}

module.exports = PublishHelper; 