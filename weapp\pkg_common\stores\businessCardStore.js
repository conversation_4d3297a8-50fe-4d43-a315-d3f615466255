/**
 * 名片管理 Store
 */
const { request } = require('../utils/request.js');
const api = require('../config/api.js');

class BusinessCardStore {
  constructor() {
    this.myCard = null;
    this.loading = false;
    this.error = null;
  }

  /**
   * 获取当前用户名片列表
   */
  async getMyCard() {
    this.loading = true;
    this.error = null;

    try {
      const response = await request({
        url: '/blade-chat/card/my-card',
        method: 'GET'
      });

      if (response.success) {
        // 后端返回的是名片列表数组
        this.myCard = response.data || [];
        return {
          success: true,
          data: this.myCard
        };
      } else {
        this.error = response.msg || '获取名片失败';
        return {
          success: false,
          message: this.error
        };
      }
    } catch (error) {
      console.error('获取名片失败:', error);
      this.error = error.message || '网络请求失败';
      return {
        success: false,
        message: this.error
      };
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取名片列表（用于搜索）
   * @param {Object} params 查询参数
   * @param {number} params.current 当前页码
   * @param {number} params.size 每页大小
   * @param {string} params.keyword 搜索关键词
   * @param {string} params.categoryName 分类名称
   * @returns {Promise<Array>} 名片列表
   */
  async getBusinessCardList(params = {}) {
    try {
      console.log('=== businessCardStore: 开始请求名片列表 ===');
      console.log('businessCardStore: 请求参数 =', JSON.stringify(params, null, 2));

      const requestData = {
        current: params.current || 1,
        size: params.size || 10,
        ...(params.keyword && { keyword: params.keyword }),
        ...(params.categoryName && { categoryName: params.categoryName })
      };

      const response = await request({
        url: '/blade-chat/card/list',
        method: 'GET',
        data: requestData
      });

      console.log('businessCardStore: 接口响应 =', response);

      if (response.success) {
        const data = response.data || {};
        const records = data.records || [];
        
        // 处理名片数据
        const processedRecords = records.map(card => this.formatCardForDisplay(card));
        
        console.log('businessCardStore: 处理后的结果 =', {
          recordsCount: processedRecords.length,
          total: data.total || 0
        });

        return processedRecords;
      } else {
        console.error('businessCardStore: 获取名片列表失败 -', response.msg);
        return [];
      }
    } catch (error) {
      console.error('=== businessCardStore: 请求名片列表接口失败 ===');
      console.error('错误:', error);
      return [];
    }
  }

  /**
   * 保存或更新名片
   * @param {Object} cardData - 名片数据
   */
  async saveCard(cardData) {
    this.loading = true;
    this.error = null;

    try {
      const response = await request({
        url: '/blade-chat/card/create',
        method: 'POST',
        data: cardData
      });

      if (response.success) {
        // 更新本地缓存
        if (Array.isArray(this.myCard)) {
          if (cardData.id) {
            // 更新现有名片
            const index = this.myCard.findIndex(card => card.id === cardData.id);
            if (index !== -1) {
              this.myCard[index] = { ...this.myCard[index], ...cardData };
            } else {
              // 如果没找到，添加新名片
              this.myCard.push(cardData);
            }
          } else {
            // 新增名片，添加到列表
            this.myCard.push(cardData);
          }
        } else {
          // 如果缓存不是数组，重新初始化
          this.myCard = [cardData];
        }
        return {
          success: true,
          message: '保存成功'
        };
      } else {
        this.error = response.msg || '保存失败';
        return {
          success: false,
          message: this.error
        };
      }
    } catch (error) {
      console.error('保存名片失败:', error);
      this.error = error.message || '网络请求失败';
      return {
        success: false,
        message: this.error
      };
    } finally {
      this.loading = false;
    }
  }

  /**
   * 获取名片详情（通过ID）
   * @param {String} cardId - 名片ID
   */
  async getCardDetail(cardId) {
    try {
      const response = await request({
        url: '/blade-chat/card/detail',
        method: 'GET',
        data: { id: cardId }
      });

      if (response.success) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          message: response.msg || '获取名片详情失败'
        };
      }
    } catch (error) {
      console.error('获取名片详情失败:', error);
      return {
        success: false,
        message: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 获取名片列表（分页）
   * @param {Object} params - 查询参数
   */
  async getCardList(params = {}) {
    try {
      const response = await request({
        url: '/blade-chat/card/list',
        method: 'GET',
        data: params
      });

      if (response.success) {
        return {
          success: true,
          data: response.data
        };
      } else {
        return {
          success: false,
          message: response.msg || '获取名片列表失败'
        };
      }
    } catch (error) {
      console.error('获取名片列表失败:', error);
      return {
        success: false,
        message: error.message || '网络请求失败'
      };
    }
  }

  /**
   * 删除名片
   * @param {String} ids - 名片ID列表，逗号分隔
   */
  async deleteCard(ids) {
    this.loading = true;
    this.error = null;

    try {
      // 后端接口需要通过URL参数传递ids
      const response = await request({
        url: `/blade-chat/card/remove?ids=${ids}`,
        method: 'POST'
      });

      if (response.success) {
        // 如果删除的是当前用户的名片，从本地缓存中移除
        if (Array.isArray(this.myCard)) {
          this.myCard = this.myCard.filter(card => !ids.includes(card.id.toString()));
        }
        return {
          success: true,
          message: '删除成功'
        };
      } else {
        this.error = response.msg || '删除失败';
        return {
          success: false,
          message: this.error
        };
      }
    } catch (error) {
      console.error('删除名片失败:', error);
      this.error = error.message || '网络请求失败';
      return {
        success: false,
        message: this.error
      };
    } finally {
      this.loading = false;
    }
  }

  /**
   * 验证名片数据
   * @param {Object} cardData - 名片数据
   */
  validateCard(cardData) {
    const errors = [];

    // 必填字段验证
    if (!cardData.fullName || cardData.fullName.trim() === '') {
      errors.push('姓名不能为空');
    }

    if (!cardData.phone || cardData.phone.trim() === '') {
      errors.push('电话不能为空');
    } else {
      // 电话格式验证
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(cardData.phone)) {
        errors.push('请输入正确的手机号码');
      }
    }

    // 邮箱格式验证（如果填写了邮箱）
    if (cardData.email && cardData.email.trim() !== '') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(cardData.email)) {
        errors.push('请输入正确的邮箱地址');
      }
    }

    // 网址格式验证（如果填写了网址）
    if (cardData.website && cardData.website.trim() !== '') {
      const urlRegex = /^https?:\/\/.+/;
      if (!urlRegex.test(cardData.website)) {
        errors.push('请输入正确的网址（以http://或https://开头）');
      }
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    };
  }

  /**
   * 格式化名片数据用于显示
   * @param {Object} cardData - 原始名片数据
   */
  formatCardForDisplay(cardData) {
    if (!cardData) return null;

    return {
      ...cardData,
      genderText: this.getGenderText(cardData.gender),
      isPublicText: cardData.isPublic === 1 ? '公开' : '私密',
      hasAvatar: !!(cardData.avatar && cardData.avatar.trim()),
      hasImages: !!(cardData.images && cardData.images.trim()),
      hasVideo: !!(cardData.video && cardData.video.trim()),
      imageList: cardData.images ? cardData.images.split(',').filter(img => img.trim()) : [],
      displayPhone: this.formatPhone(cardData.phone),
      displayEmail: cardData.email || '未填写',
      displayAddress: cardData.address || '未填写',
      displayWebsite: cardData.website || '未填写',
      displayWeixin: cardData.weixin || '未填写'
    };
  }

  /**
   * 获取性别文本
   * @param {Number} gender - 性别代码
   */
  getGenderText(gender) {
    switch (gender) {
      case 1: return '男';
      case 2: return '女';
      default: return '保密';
    }
  }

  /**
   * 格式化电话号码显示
   * @param {String} phone - 电话号码
   */
  formatPhone(phone) {
    if (!phone) return '';
    // 手机号码中间4位用*替换
    if (phone.length === 11) {
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    }
    return phone;
  }

  /**
   * 获取状态
   */
  getState() {
    return {
      myCard: this.myCard,
      loading: this.loading,
      error: this.error
    };
  }

  /**
   * 清理数据
   */
  clear() {
    this.myCard = null;
    this.loading = false;
    this.error = null;
  }
}

// 创建单例实例
const businessCardStore = new BusinessCardStore();

module.exports = businessCardStore;
