const { request } = require('./request');
const { buildQueryString } = require('./util');
// 登录方法
const login = () => {
  return new Promise((resolve, reject) => {
    // 获取微信登录凭证
    wx.login({
      success: async (loginRes) => {
        try {
          // 构建URL参数
          const params = {
            tenantId: '000000',
            source: 'wechat_mini_app',
            code: loginRes.code,
            grantType: 'wechat_mini_app',
            scope: 'all'
          };
          const queryString = buildQueryString(params);
          
          // 调用后端登录接口
          const res = await request({
            url: `/blade-auth/token?${queryString}`,
            method: 'POST'
          });

          if (res.success) {
            // 保存token
            wx.setStorageSync('token',{
              value: res.data.accessToken,
              datetime: Date.now(),
              'refreshToken':res.data.refreshToken
            });
            
            // 等待获取用户信息完成
            const userInfo = await getRemoteUserInfo();
            if (!userInfo) {
              throw new Error('获取用户信息失败');
            }
            
            resolve(userInfo);
          } else {
            throw new Error(res.msg);
          }
        } catch (error) {
          reject(error);
        }
      },
      fail: (error) => {
        reject(new Error('微信登录失败'));
      }
    });
  });
};

// 登出方法
const logout = () => {
  // 清除本地存储的认证信息
  wx.removeStorageSync('token');
  wx.removeStorageSync('refreshToken');
  wx.removeStorageSync('userInfo');
  
  // 跳转到登录页
  // wx.navigateTo({
  //   url: '/pages/login/login'
  // });
};

// 检查登录状态
const checkLoginStatus = () => {
  const token = wx.getStorageSync('token');
  const userInfo = wx.getStorageSync('userInfo');
  return !!(token && userInfo);
};

// 获取用户信息
const getUserInfo = () => {
  return wx.getStorageSync('userInfo');
};

// 获取远程用户信息
const getRemoteUserInfo = async () => {
  try {
    const res = await request({
      url: '/blade-chat/user/info',
      method: 'GET'
    });

    if (res.code === 200) {
      // 保存用户信息到缓存
      wx.setStorageSync('userInfo', res.data);
      return res.data;
    } else {
      throw new Error(res.msg);
    }
  } catch (error) {
    console.error('获取用户信息失败：', error);
    throw error;
  }
};

export const refreshToken = (refreshToken) => request({
  url: '/blade-auth/token',
  method: 'POST',
  data: {
    refreshToken,
    grantType: 'refresh_token',
    scope: 'all',
  }
});

module.exports = {
  login,
  logout,
  checkLoginStatus,
  getUserInfo,
  getRemoteUserInfo,
  request
}; 