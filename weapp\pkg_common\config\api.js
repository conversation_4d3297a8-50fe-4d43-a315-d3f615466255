/**
 * API配置文件
 */
const config = {
  // API基础地址
  baseUrl: 'http://localhost',
  
  // 腾讯地图配置
  map: {
    // 腾讯地图API key（需要替换为实际的key）
    key: 'GU3BZ-2WLKT-AMOXL-LUR3F-RJHJ6-O4B3Y',
    // 腾讯地图API基础地址
    baseUrl: 'https://apis.map.qq.com/ws/',
    // 逆地址解析接口
    geocoder: 'geocoder/v1/'
  },
  
  // 文件上传相关接口
  fileUpload: {
    // 单文件上传
    upload: '/blade-system/file-upload/upload',
    // 批量文件上传
    uploadBatch: '/blade-system/file-upload/upload-batch',
    // 获取文件URL
    getFileUrl: '/blade-system/file-upload/url',
    // 删除文件
    removeFile: '/blade-system/file-upload/remove',
    // 获取文件列表
    getFileList: '/blade-system/file-upload/list',
    // 根据业务获取文件
    getFilesByBusiness: '/blade-system/file-upload/business',
    // 获取文件统计
    getFileStats: '/blade-system/file-upload/stats',
    // 清理过期文件
    cleanExpiredFiles: '/blade-system/file-upload/clean-expired',
    // 存储配置
    storageConfig: '/blade-system/file-upload/storage-config'
  },
  
  // 评论相关接口
  comment: {
    // 添加评论
    add: '/blade-chat/feedback/comment/add',
    // 回复评论
    reply: '/blade-chat/feedback/comment/reply',
    // 获取评论列表
    list: '/blade-chat/feedback/comment/list',
    // 获取回复列表
    replies: '/blade-chat/feedback/comment/replies',
    // 删除评论
    remove: '/blade-chat/feedback/comment/remove',
    // 点赞评论
    like: '/blade-chat/feedback/comment/like',
    // 获取评论详情
    detail: '/blade-chat/feedback/comment/detail'
  },

  // 反馈相关接口
  feedback: {
    // 提交反馈
    submit: '/blade-chat/feedback/submit',
    // 获取反馈列表（帖子详情页面使用）
    page: '/blade-chat/feedback/page',
    // 获取反馈列表（通用）
    list: '/blade-chat/feedback/list',
    // 标记有帮助
    helpful: '/blade-chat/feedback/helpful',
    // 获取反馈标签
    tags: '/blade-chat/feedback/getTagsByCategory',
    // 获取热门标签
    hotTags: '/blade-chat/feedback/getHotTags'
  },

  // 帖子相关接口
  post: {
    // 帖子详情
    detail: '/blade-chat/post/detail',
    // 点赞帖子
    like: '/blade-chat/post/like',
    // 收藏帖子
    favorite: '/blade-chat/post/favorite',
    // 获取帖子列表
    list: '/blade-chat/post/list',
    // 发布帖子
    publish: '/blade-chat/post/publish'
  },

  // 签到相关接口
  signin: {
    // 获取签到信息
    info: '/blade-chat/signin/info',
    // 执行签到
    doSignin: '/blade-chat/signin/do',
    // 获取月度签到记录
    monthRecord: '/blade-chat/signin/record',
    // 获取签到统计
    stats: '/blade-chat/signin/stats',
    // 获取签到记录列表（分页）
    records: '/blade-chat/signin/records',
    // 获取签到统计汇总
    summary: '/blade-chat/signin/summary',
    // 查询用户是否中奖
    queryUserWin: '/blade-chat/signin/queryUserWin'
  },

  // 用户相关接口
  user: {
    // 获取用户信息
    info: '/blade-chat/user/info',
    // 更新用户信息
    update: '/blade-chat/user/update',
    // 获取用户统计
    stats: '/blade-chat/user/stats',
    // 获取用户积分
    points: '/blade-chat/user/points'
  },

  // 名片相关接口
  businessCard: {
    // 获取当前用户名片详情
    myCard: '/blade-chat/user/card/my-card',
    // 获取名片详情
    detail: '/blade-chat/user/card/detail',
    // 获取名片列表
    page: '/blade-chat/user/card/page',
    // 保存名片
    save: '/blade-chat/user/card/save',
    // 更新名片
    update: '/blade-chat/user/card/update',
    // 提交名片（新增或修改）
    submit: '/blade-chat/user/card/submit',
    // 删除名片
    remove: '/blade-chat/user/card/remove'
  },

  // 机构相关接口
  institution: {
    // 获取机构列表（分页）
    page: '/blade-chat-open/institution/page',    // 获取机构详情
    detail: '/blade-chat-open/institution/detail',
    // 获取我加入的机构列表
    myInstitutions: '/blade-chat/institution/my-institutions',
    // 申请加入机构
    apply: '/blade-chat/institution/apply',
    // 创建机构
    create: '/blade-chat/institution/create',
    // 更新机构信息
    update: '/blade-chat/institution/update',
    // 删除机构
    remove: '/blade-chat/institution/remove'
  },

  // 机构分类相关接口
  institutionType: {
    // 获取机构分类列表
    list: '/blade-chat-open/institution/type/list',
    // 获取机构分类详情
    detail: '/blade-chat-open/institution-type/detail'
  },

  // 认证相关
  auth: {
    tenantId: '000000',
    basicAuth: 'Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U='
  },
  
  // 文件上传配置
  upload: {
    // 最大文件大小（字节）
    maxFileSize: 10 * 1024 * 1024, // 10MB
    // 支持的文件类型
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    // 最大图片数量
    maxImageCount: 6,
    // 上传来源
    uploadSource: 'miniapp'
  }
};

module.exports = config; 
