<view class="favorite-tags-card">
  <view class="favorite-tags-header">
    <text class="favorite-tags-title">收藏夹</text>
    <text class="favorite-tags-more" bindtap="onMore">查看全部</text>
  </view>
  
  <!-- 有收藏标签时显示 -->
  <view wx:if="{{tags && tags.length > 0}}" class="favorite-tags-list">
    <block wx:for="{{tags}}" wx:key="text">
      <text class="favorite-tag {{tag.selected ? 'selected' : ''}}" bindtap="onTagTap" data-index="{{index}}">{{tag.text}}</text>
    </block>
  </view>
  
  <!-- 空状态显示 -->
  <view wx:else class="favorite-tags-empty">
    <image class="empty-icon" src="/assets/images/mine/collections.png" mode="aspectFit"></image>
    <text class="empty-text">暂无收藏内容</text>
    <text class="empty-desc">浏览时点击收藏，内容会出现在这里</text>
  </view>
</view> 