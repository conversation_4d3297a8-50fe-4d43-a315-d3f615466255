/* 我的收藏页面样式 */
.collections-page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 32rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-back {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 40rpx;
  height: 40rpx;
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  text-align: center;
  flex: 1;
}

.header-action {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-text {
  font-size: 28rpx;
  color: #ff6b6b;
}

/* 标签筛选 */
.tags-filter {
  background: #fff;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.tags-scroll {
  white-space: nowrap;
  padding: 0 32rpx;
}

.tag-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.tag-item.active {
  background: #ff6b6b;
  color: #fff;
}

/* 收藏列表滚动区域 */
.collections-scroll {
  flex: 1;
  padding: 24rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 帖子列表 */
.posts-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.post-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  gap: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.post-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 帖子图片 */
.post-image-container {
  position: relative;
  width: 160rpx;
  height: 120rpx;
  flex-shrink: 0;
}

.post-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.post-category {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 8rpx;
  border-radius: 8rpx;
}

/* 帖子信息 */
.post-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.post-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

.post-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  margin-bottom: 16rpx;
}

.post-meta {
  display: flex;
  gap: 24rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.meta-text {
  font-size: 24rpx;
  color: #999;
}

/* 操作按钮 */
.post-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.action-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.action-btn:active {
  background: #ff6b6b;
  transform: scale(0.9);
}

.action-btn:active .action-icon {
  filter: brightness(0) invert(1);
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  opacity: 0.6;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  padding: 20rpx 40rpx;
  background: #ff6b6b;
  color: #fff;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.empty-action:active {
  background: #e55555;
  transform: scale(0.95);
}

.action-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}
