<!--ai.wxml-->
<view class="container">
  <!-- 信息展示区域 -->
  <!-- <view class="info-section" style="margin-top: {{menuButtonTop + menuButtonHeight + 8}}px;">
    <view class="info-content">
      <view class="info-title">问AI小易</view>
      <view class="info-desc">你的智能助手，解答任何问题</view>
    </view>
  </view> -->

  <!-- 日报内容区 -->
  <view class="daily-news">
    <view class="header">
      <text class="title">精选日报</text>
      <view class="date">
        <text class="date-text">03.07</text>
        <text class="weekday">五</text>
      </view>
    </view>

    <!-- 新闻列表 -->
    <!-- <view class="news-list">
      <view class="news-item" wx:for="{{newsList}}" wx:key="index">
        <text class="news-index">{{index + 1}}</text>
        <text class="news-content">{{item.content}}</text>
      </view>
    </view> -->

    <!-- 底部按钮 -->
    <!-- <view class="bottom-actions">
      <view class="action-button" bindtap="onDiscoverMore">发现更多</view>
      <view class="action-button refresh-button" bindtap="onRefresh">换一批</view>
    </view> -->
  </view>

  <!-- 输入框区域 -->
  <!-- <view class="input-section">
    <view class="input-box {{isVoiceMode ? 'voice-mode' : ''}}">
      <image class="voice-icon {{isVoiceMode ? 'active' : ''}}" 
             src="{{isVoiceMode ? '/assets/images/common/keyboard.png' : '/assets/images/common/voice.png'}}" 
             mode="aspectFit" 
             bindtap="toggleInputMode"></image>
      <block wx:if="{{!isVoiceMode}}">
        <input class="chat-input" 
               value="{{inputValue}}" 
               bindinput="onInput"
               bindconfirm="onSendTap"
               placeholder="有什么问题尽管问我" 
               confirm-type="send"/>
        <view class="right-tools">
          <image class="plus-icon" src="/assets/images/common/plus.png" mode="aspectFit" bindtap="onPlusTap"></image>
          <image class="send-icon" src="/assets/images/common/send.png" mode="aspectFit" bindtap="onSendTap"></image>
        </view>
      </block>
      <block wx:else>
        <view class="voice-input" 
              bindtouchstart="startVoiceRecord" 
              bindtouchend="endVoiceRecord"
              bindtouchmove="moveVoiceRecord">
          {{recordingTip}}
        </view>
      </block>
    </view>
  </view> -->
</view> 