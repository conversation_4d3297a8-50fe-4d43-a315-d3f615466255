/**
 * API配置文件
 */
const config = {
  // API基础地址
  baseUrl: 'http://localhost',
  
  // 文件上传相关接口
  fileUpload: {
    // 单文件上传
    upload: '/blade-system/file-upload/upload',
    // 批量文件上传
    uploadBatch: '/blade-system/file-upload/upload-batch',
    // 获取文件URL
    getFileUrl: '/blade-system/file-upload/url',
    // 删除文件
    removeFile: '/blade-system/file-upload/remove',
    // 获取文件列表
    getFileList: '/blade-system/file-upload/list',
    // 根据业务获取文件
    getFilesByBusiness: '/blade-system/file-upload/business',
    // 获取文件统计
    getFileStats: '/blade-system/file-upload/stats',
    // 清理过期文件
    cleanExpiredFiles: '/blade-system/file-upload/clean-expired',
    // 存储配置
    storageConfig: '/blade-system/file-upload/storage-config'
  },
  
  // 认证相关
  auth: {
    tenantId: '000000',
    basicAuth: 'Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U='
  },
  
  // 文件上传配置
  upload: {
    // 最大文件大小（字节）
    maxFileSize: 10 * 1024 * 1024, // 10MB
    // 支持的文件类型
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    // 最大图片数量
    maxImageCount: 6,
    // 上传来源
    uploadSource: 'miniapp'
  }
};

module.exports = config; 
