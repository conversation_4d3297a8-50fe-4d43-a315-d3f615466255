/* 本地名片库页面样式 */
.card-library-page {
  min-height: 100vh;
  background: #f8f9fa;
  display: flex;
  flex-direction: column;
}

/* 页面头部 */
/* .page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
  padding: 16rpx;
  padding-top: calc(16rpx + env(safe-area-inset-top));
}

.header-content {
  flex: 1;
}

.header-title {
  font-size: 30rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 2rpx;
}

.header-subtitle {
  font-size: 20rpx;
  color: rgba(255, 255, 255, 0.8);
}

.header-actions {
  display: flex;
  align-items: center;
}

.search-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
} */



/* 搜索栏 */
.search-container {
  background: white;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
  /* border-bottom: 1rpx solid #f0f0f0; */
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  filter: brightness(0) invert(1);
}

.search-box {
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 24rpx;
  padding: 16rpx 24rpx;
  gap: 16rpx;
}

.search-box .search-icon {
  width: 28rpx;
  height: 28rpx;
  opacity: 0.6;
  filter: none;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.input-placeholder {
  color: #9CA3AF;
}

.clear-btn {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-icon {
  font-size: 32rpx;
  color: #999;
}

.search-cancel {
  font-size: 28rpx;
  color: #ff6b6b;
}

/* 筛选栏 */
.filter-bar {
  background: white;
  padding: 24rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.filter-scroll {
  flex: 1;
  white-space: nowrap;
  padding: 0 32rpx;
}

.filter-item {
  display: inline-block;
  padding: 12rpx 24rpx;
  margin-right: 16rpx;
  background: #f5f5f5;
  color: #666;
  font-size: 26rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.filter-item.active {
  background: #ff6b6b;
  color: white;
}

.filter-more {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 名片列表滚动区域 */
.card-list-scroll {
  margin-top: 12rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24rpx;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 28rpx;
}

/* 名片列表 */
.card-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  margin: 0 20rpx;
}

.card-item {
  background: white;
  border-radius: 16rpx;
  padding: 32rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.card-item:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

/* 名片头部 */
.card-header {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  margin-bottom: 24rpx;
}

.card-avatar {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: #f5f5f5;
}

.card-info {
  flex: 1;
}

.card-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.card-position {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 4rpx;
}

.card-company {
  font-size: 24rpx;
  color: #999;
}

.card-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4rpx;
  padding: 8rpx;
  border-radius: 8rpx;
  transition: all 0.3s ease;
}

.like-btn.active {
  background: rgba(255, 107, 107, 0.1);
}

.action-icon {
  font-size: 32rpx;
}

.action-count {
  font-size: 20rpx;
  color: #999;
}

/* 名片内容 */
.card-content {
  margin-bottom: 24rpx;
}

.card-profile {
  margin-bottom: 16rpx;
}

.profile-text {
  font-size: 28rpx;
  color: #555;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  /* -webkit-line-clamp: 2; */
  overflow: hidden;
}

.card-contact {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.contact-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.6;
}

.contact-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}

.distance-text {
  font-size: 24rpx;
  color: #ff6b6b;
  font-weight: 500;
}

/* 名片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 24rpx;
  border-top: 1rpx solid #f0f0f0;
}

.card-stats {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.stat-item {
  font-size: 24rpx;
  color: #999;
}

.stat-time {
  font-size: 24rpx;
  color: #ccc;
}

.card-operations {
  display: flex;
  gap: 16rpx;
}

.operation-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;
}

.operation-btn.active {
  background: #ff6b6b;
  color: white;
}

.btn-icon {
  font-size: 24rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 60rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.empty-action {
  padding: 20rpx 40rpx;
  background: #ff6b6b;
  color: white;
  border-radius: 24rpx;
  transition: all 0.3s ease;
}

.empty-action:active {
  background: #e55555;
  transform: scale(0.95);
}

.action-text {
  font-size: 28rpx;
  font-weight: 500;
}

/* 加载更多 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.load-more-text {
  font-size: 26rpx;
  color: #999;
}

/* 没有更多 */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #ccc;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.favorite-modal,
.filter-modal {
  background: white;
  border-radius: 16rpx;
  width: 640rpx;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  margin: 40rpx;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
}

.modal-content {
  flex: 1;
  padding: 32rpx;
  overflow-y: auto;
}

.modal-footer {
  display: flex;
  gap: 24rpx;
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}

.modal-btn {
  flex: 1;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.cancel-btn,
.reset-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:active,
.reset-btn:active {
  background: #e9ecef;
}

.confirm-btn {
  background: #ff6b6b;
  color: white;
}

.confirm-btn:active {
  background: #e55555;
}

/* 收藏弹窗表单 */
.form-item {
  margin-bottom: 32rpx;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.category-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.category-item {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 20rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff6b6b;
  color: white;
}

.category-input {
  flex: 1;
  min-width: 200rpx;
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #333;
}

.remark-textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx;
  background: #f5f5f5;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  box-sizing: border-box;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 筛选弹窗 */
.filter-section {
  margin-bottom: 40rpx;
}

.section-title {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: 500;
}

.range-options,
.industry-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.range-item,
.industry-item {
  padding: 12rpx 24rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 20rpx;
  font-size: 26rpx;
  transition: all 0.3s ease;
}

.range-item.active,
.industry-item.active {
  background: #ff6b6b;
  color: white;
}

/* 联系人提示样式 */
.contact-tip {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 12rpx;
  z-index: 1000;
  animation: fadeInOut 2s ease-in-out;
}

.tip-text {
  font-size: 28rpx;
  color: white;
}

@keyframes fadeInOut {
  0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
  20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
  100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
}
