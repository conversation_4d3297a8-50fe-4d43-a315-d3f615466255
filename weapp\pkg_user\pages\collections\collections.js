// 我的收藏页面
const favoriteService = require('../../../services/favoriteService');

Page({
  data: {
    // 收藏列表数据
    posts: [],
    tags: [],
    selectedTag: '',

    // 分页参数
    current: 1,
    size: 10,
    total: 0,

    // 状态控制
    loading: false,
    loadingMore: false,
    refreshing: false,
    noMore: false,

    // 初始标签（从上一页传递）
    initialTag: ''
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('收藏页面参数:', options);

    // 获取传递的标签参数
    if (options.tag) {
      this.setData({
        selectedTag: decodeURIComponent(options.tag),
        initialTag: decodeURIComponent(options.tag)
      });
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '我的收藏'
    });

    // 加载数据
    this.loadTags();
    this.loadPosts(true);
  },

  /**
   * 页面显示
   */
  onShow() {
    // 如果从其他页面返回，刷新数据
    if (this.data.posts.length > 0) {
      this.loadPosts(true);
    }
  },

  /**
   * 加载收藏标签
   */
  async loadTags() {
    try {
      const tags = await favoriteService.getFavoriteTags();
      this.setData({ tags });
    } catch (error) {
      console.error('加载收藏标签失败:', error);
    }
  },

  /**
   * 加载收藏帖子列表
   */
  async loadPosts(refresh = false) {
    if (this.data.loading || this.data.loadingMore) return;

    // 设置加载状态
    if (refresh) {
      this.setData({
        loading: true,
        current: 1,
        noMore: false
      });
    } else {
      this.setData({ loadingMore: true });
    }

    try {
      const params = {
        current: refresh ? 1 : this.data.current,
        size: this.data.size
      };

      // 如果有选中的标签，添加标签筛选
      if (this.data.selectedTag) {
        params.categoryName = this.data.selectedTag;
      }

      const result = await favoriteService.getFavoritePosts(params);

      if (result.success) {
        const newPosts = result.data || [];

        this.setData({
          posts: refresh ? newPosts : [...this.data.posts, ...newPosts],
          total: result.total,
          current: result.current + 1,
          noMore: newPosts.length < this.data.size
        });
      } else {
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载收藏列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      });
    }
  },

  /**
   * 标签筛选
   */
  onTagFilter(e) {
    const tag = e.currentTarget.dataset.tag;

    if (tag === this.data.selectedTag) return;

    this.setData({ selectedTag: tag });
    this.loadPosts(true);
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.loadTags();
    this.loadPosts(true);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (!this.data.noMore && !this.data.loadingMore) {
      this.loadPosts(false);
    }
  },

  /**
   * 帖子点击事件
   */
  onPostTap(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    });
  },

  /**
   * 取消收藏
   */
  async onRemoveFavorite(e) {
     // 阻止事件冒泡

    const postId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    try {
      wx.showModal({
        title: '提示',
        content: '确定要取消收藏这条内容吗？',
        success: async (res) => {
          if (res.confirm) {
            const result = await favoriteService.toggleFavorite(postId);

            if (result.success) {
              // 从列表中移除
              const posts = [...this.data.posts];
              posts.splice(index, 1);
              this.setData({ posts });

              wx.showToast({
                title: '取消收藏成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: result.message || '操作失败',
                icon: 'none'
              });
            }
          }
        }
      });
    } catch (error) {
      console.error('取消收藏失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 清空所有收藏
   */
  onClearAll() {
    if (this.data.posts.length === 0) {
      wx.showToast({
        title: '暂无收藏内容',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '提示',
      content: '确定要清空所有收藏吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await favoriteService.clearFavorites();

            if (result.success) {
              this.setData({
                posts: [],
                total: 0,
                current: 1,
                noMore: false
              });

              wx.showToast({
                title: '清空成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: result.message || '清空失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('清空收藏失败:', error);
            wx.showToast({
              title: '操作失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 去逛逛
   */
  onGoExplore() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
});