// 我的收藏页面
const favoriteService = require('../../../services/favoriteService');

Page({
  data: {
    // 收藏列表数据
    posts: [],
    tags: ['求职招聘', '房屋租售', '生活服务', '二手交易'],
    selectedTag: '',

    // 分页参数
    current: 1,
    size: 10,
    total: 0,

    // 状态控制
    loading: false,
    loadingMore: false,
    refreshing: false,
    noMore: false,

    // 搜索相关
    searchKeyword: '',
    searchFocus: false,

    // 筛选相关
    selectedFilter: '',
    showFilterModal: false,

    // 初始标签（从上一页传递）
    initialTag: ''
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    console.log('收藏页面参数:', options);

    // 获取传递的标签参数
    if (options.tag) {
      this.setData({
        selectedTag: decodeURIComponent(options.tag),
        initialTag: decodeURIComponent(options.tag)
      });
    }

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '我的收藏'
    });
  },

  /**
   * 页面准备完毕
   */
  onReady() {
    // 页面渲染完成后加载数据
    this.loadTags();
    this.loadPosts(true);
  },

  /**
   * 页面显示
   */
  onShow() {
    // 每次显示页面时都刷新数据，确保数据是最新的
    this.loadTags();
    this.loadPosts(true);
  },

  /**
   * 加载收藏标签
   */
  async loadTags() {
    try {
      console.log('开始加载收藏标签...');
      const tags = await favoriteService.getFavoriteTags();
      console.log('收藏标签加载结果:', tags);
      this.setData({ tags });
    } catch (error) {
      console.error('加载收藏标签失败:', error);
      // 设置默认标签以便测试
      this.setData({ tags: ['全部', '求职招聘', '房屋租售'] });
    }
  },

  /**
   * 加载收藏帖子列表
   */
  async loadPosts(refresh = false) {
    console.log('开始加载收藏帖子列表, refresh:', refresh);
    if (this.data.loading || this.data.loadingMore) {
      console.log('正在加载中，跳过本次请求');
      return;
    }

    // 设置加载状态
    if (refresh) {
      this.setData({
        loading: true,
        current: 1,
        noMore: false
      });
    } else {
      this.setData({ loadingMore: true });
    }

    try {
      const params = {
        current: refresh ? 1 : this.data.current,
        size: this.data.size
      };
      console.log('请求参数:', params);

      // 添加搜索条件
      if (this.data.searchKeyword) {
        params.keyword = this.data.searchKeyword;
      }

      // 添加筛选条件
      if (this.data.selectedFilter === 'recent') {
        params.orderBy = 'create_time';
        params.orderDirection = 'desc';
      }

      // 如果有选中的标签，添加标签筛选
      if (this.data.selectedTag) {
        params.categoryName = this.data.selectedTag;
      }

      // 如果有选中的筛选器且不是recent，作为标签筛选
      if (this.data.selectedFilter && this.data.selectedFilter !== 'recent' && this.data.selectedFilter !== '') {
        params.categoryName = this.data.selectedFilter;
      }

      const result = await favoriteService.getFavoritePosts(params);
      console.log('收藏帖子加载结果:', result);

      if (result.success) {
        const newPosts = result.data || [];
        console.log('新加载的帖子数量:', newPosts.length);

        this.setData({
          posts: refresh ? newPosts : [...this.data.posts, ...newPosts],
          total: result.total,
          current: result.current + 1,
          noMore: newPosts.length < this.data.size
        });

        console.log('更新后的帖子总数:', this.data.posts.length);
      } else {
        console.error('加载收藏帖子失败:', result.message);
        wx.showToast({
          title: result.message || '加载失败',
          icon: 'none'
        });
      }
    } catch (error) {
      console.error('加载收藏列表失败:', error);
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({
        loading: false,
        loadingMore: false,
        refreshing: false
      });
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    this.setData({ searchKeyword: e.detail.value });
  },

  /**
   * 执行搜索
   */
  onSearch() {
    this.loadPosts(true);
  },

  /**
   * 清空搜索
   */
  onClearSearch() {
    this.setData({ searchKeyword: '' });
    this.loadPosts(true);
  },

  /**
   * 筛选点击
   */
  onFilterTap(e) {
    const filter = e.currentTarget.dataset.filter;
    this.setData({ selectedFilter: filter });
    this.loadPosts(true);
  },

  /**
   * 显示更多筛选
   */
  onShowMoreFilter() {
    this.setData({ showFilterModal: true });
  },

  /**
   * 隐藏筛选弹窗
   */
  onHideFilterModal() {
    this.setData({ showFilterModal: false });
  },

  /**
   * 标签筛选
   */
  onTagFilter(e) {
    const tag = e.currentTarget.dataset.tag;

    if (tag === this.data.selectedTag) return;

    this.setData({ selectedTag: tag });
    this.loadPosts(true);
  },

  /**
   * 下拉刷新
   */
  onRefresh() {
    this.setData({ refreshing: true });
    this.loadTags();
    this.loadPosts(true);
  },

  /**
   * 上拉加载更多
   */
  onReachBottom() {
    if (!this.data.noMore && !this.data.loadingMore) {
      this.loadPosts(false);
    }
  },

  /**
   * 帖子点击事件
   */
  onPostTap(e) {
    const postId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/post-detail/post-detail?id=${postId}`
    });
  },

  /**
   * 取消收藏
   */
  async onRemoveFavorite(e) {
     // 阻止事件冒泡

    const postId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;

    try {
      wx.showModal({
        title: '提示',
        content: '确定要取消收藏这条内容吗？',
        success: async (res) => {
          if (res.confirm) {
            const result = await favoriteService.toggleFavorite(postId);

            if (result.success) {
              // 从列表中移除
              const posts = [...this.data.posts];
              posts.splice(index, 1);
              this.setData({ posts });

              wx.showToast({
                title: '取消收藏成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: result.message || '操作失败',
                icon: 'none'
              });
            }
          }
        }
      });
    } catch (error) {
      console.error('取消收藏失败:', error);
      wx.showToast({
        title: '操作失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 清空所有收藏
   */
  onClearAll() {
    if (this.data.posts.length === 0) {
      wx.showToast({
        title: '暂无收藏内容',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '提示',
      content: '确定要清空所有收藏吗？此操作不可恢复。',
      success: async (res) => {
        if (res.confirm) {
          try {
            const result = await favoriteService.clearFavorites();

            if (result.success) {
              this.setData({
                posts: [],
                total: 0,
                current: 1,
                noMore: false
              });

              wx.showToast({
                title: '清空成功',
                icon: 'success'
              });
            } else {
              wx.showToast({
                title: result.message || '清空失败',
                icon: 'none'
              });
            }
          } catch (error) {
            console.error('清空收藏失败:', error);
            wx.showToast({
              title: '操作失败，请重试',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  /**
   * 分享收藏内容
   */
  onShareTap(e) {
     // 阻止事件冒泡

    const postId = e.currentTarget.dataset.id;
    const index = e.currentTarget.dataset.index;
    const post = this.data.posts[index];

    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });

    // 触发分享
    wx.showActionSheet({
      itemList: ['分享给朋友', '分享到朋友圈', '复制链接'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            // 分享给朋友
            this.shareToFriend(post);
            break;
          case 1:
            // 分享到朋友圈
            this.shareToTimeline(post);
            break;
          case 2:
            // 复制链接
            this.copyLink(post);
            break;
        }
      }
    });
  },

  /**
   * 分享给朋友
   */
  shareToFriend(post) {
    // 这里可以设置分享内容
    wx.showToast({
      title: '请点击右上角分享',
      icon: 'none'
    });
  },

  /**
   * 分享到朋友圈
   */
  shareToTimeline(post) {
    wx.showToast({
      title: '请点击右上角分享到朋友圈',
      icon: 'none'
    });
  },

  /**
   * 复制链接
   */
  copyLink(post) {
    wx.setClipboardData({
      data: `${post.title} - 来自易找易发`,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 导出收藏列表
   */
  onExportCollections() {
    if (this.data.posts.length === 0) {
      wx.showToast({
        title: '暂无收藏内容',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '导出收藏',
      content: '是否要导出收藏列表？',
      success: (res) => {
        if (res.confirm) {
          this.exportToText();
        }
      }
    });
  },

  /**
   * 导出为文本
   */
  exportToText() {
    let content = '我的收藏列表\n\n';
    this.data.posts.forEach((post, index) => {
      content += `${index + 1}. ${post.title}\n`;
      content += `   分类：${post.categoryName || '其他'}\n`;
      content += `   内容：${post.content || '无描述'}\n`;
      content += `   时间：${post.createTime}\n\n`;
    });

    wx.setClipboardData({
      data: content,
      success: () => {
        wx.showToast({
          title: '收藏列表已复制到剪贴板',
          icon: 'success'
        });
      }
    });
  },

  /**
   * 去逛逛
   */
  onGoExplore() {
    wx.switchTab({
      url: '/pages/index/index'
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
});