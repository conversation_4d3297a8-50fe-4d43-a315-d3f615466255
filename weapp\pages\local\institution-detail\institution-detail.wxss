/* 机构详情页面样式 */

.institution-detail-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f0f0f0;
  border-top: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #ff6b6b;
  color: white;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 内容容器 */
.content-container {
  background-color: white;
}

/* 机构头部 */
.institution-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%);
  padding: 40rpx 32rpx;
  color: white;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.institution-main {
  flex: 1;
}

.institution-name {
  font-size: 40rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.institution-rating {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.stars {
  display: flex;
  margin-right: 16rpx;
}

.star {
  color: rgba(255, 255, 255, 0.3);
  font-size: 28rpx;
  margin-right: 4rpx;
}

.star.active {
  color: #ffd700;
}

.rating-text {
  font-size: 28rpx;
  color: white;
  margin-right: 16rpx;
  font-weight: 500;
}

.review-count {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.institution-address {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

.address-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;
  opacity: 0.8;
}

.address-text {
  flex: 1;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.favorite-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 40rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
}

.favorite-btn.active {
  background-color: rgba(255, 255, 255, 0.9);
  color: #ff6b6b;
  border-color: rgba(255, 255, 255, 0.9);
}

.favorite-btn .btn-icon {
  font-size: 28rpx;
}

.favorite-btn .btn-text {
  font-size: 26rpx;
}

/* Tab导航 */
.tab-nav {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 30rpx;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
}

.tab-item.active {
  color: #ff6b6b;
  font-weight: 600;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #ff6b6b;
  border-radius: 2rpx;
}

/* Tab内容 */
.tab-content {
  background-color: #f8f9fa;
  min-height: 400rpx;
}

/* 机构信息内容 */
.info-content {
  padding: 32rpx;
}

.info-section {
  background-color: white;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.section-icon {
  font-size: 32rpx;
  margin-right: 16rpx;
  opacity: 0.8;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.description {
  line-height: 1.8;
  color: #555;
}

.business-hours {
  color: #555;
  font-weight: 500;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 26rpx;
  font-weight: 500;
}

.image-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.gallery-item {
  position: relative;
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.gallery-image {
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.gallery-item:active .gallery-image {
  transform: scale(1.05);
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:active .image-overlay {
  opacity: 1;
}

.preview-icon {
  font-size: 48rpx;
  color: white;
}

/* 帖子内容 */
.posts-content {
  padding: 20rpx;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

.post-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.post-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease;
}

.post-item:active {
  transform: scale(0.98);
}

.post-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.post-author {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.author-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f5f5f5;
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.author-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.post-time {
  font-size: 24rpx;
  color: #999;
}

.post-category {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
}

.post-content {
  margin-bottom: 20rpx;
}

.post-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.post-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.post-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-top: 20rpx;
}

.post-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
}

.post-actions {
  display: flex;
  align-items: center;
  gap: 40rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx;
  border-radius: 20rpx;
  transition: background-color 0.2s ease;
}

.action-item:active {
  background-color: #f5f5f5;
}

.action-item.liked .action-icon {
  filter: hue-rotate(320deg) saturate(2);
}

.action-item.liked .action-text {
  color: #ff6b35;
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
}

.action-text {
  font-size: 26rpx;
  color: #666;
}



/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: white;
  border-top: 1rpx solid #f0f0f0;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  z-index: 100;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border: none;
  border-radius: 16rpx;
  padding: 24rpx 16rpx;
  margin: 0 8rpx;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
}

.action-btn:active {
  background-color: #e9ecef;
  transform: scale(0.95);
}

.action-btn.primary {
  background: linear-gradient(135deg, #ff6b6b, #ff8a8a);
  color: white;
}

.action-btn.primary:active {
  background: linear-gradient(135deg, #e55555, #ff6b6b);
}

.action-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 8rpx;
}

.action-btn .action-icon {
  font-size: 40rpx;
}

.action-btn.primary .action-icon {
  filter: brightness(0) invert(1);
}

.action-text {
  font-size: 26rpx;
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .institution-header {
    padding: 30rpx;
  }

  .institution-name {
    font-size: 32rpx;
  }

  .tab-content {
    min-height: 300rpx;
  }

  .info-content,
  .posts-content,
  .comments-content {
    padding: 30rpx;
  }
}

/* 评论相关样式 */
.comments-content {
  padding: 0;
}

.comment-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.stats-item {
  display: flex;
  align-items: baseline;
  gap: 8rpx;
}

.stats-number {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.stats-label {
  font-size: 24rpx;
  color: #666;
}

.add-comment-btn {
  padding: 12rpx 24rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 20rpx;
  font-size: 26rpx;
  transition: all 0.2s;
}

.add-comment-btn:active {
  background: #0056b3;
  transform: scale(0.95);
}

.add-comment-text {
  font-size: 26rpx;
}

.comment-section {
  background: #fff;
}

.empty-comments {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 32rpx;
  line-height: 1.5;
}

.empty-action-btn {
  padding: 16rpx 32rpx;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 24rpx;
  font-size: 28rpx;
}

.load-more {
  padding: 32rpx;
  text-align: center;
  background: #fff;
  border-top: 1rpx solid #f0f0f0;
}

.load-more-text {
  font-size: 26rpx;
  color: #007aff;
}