<view class="container">
  <!-- 搜索框 -->
  <view class="search-header">
    <view class="search-bar">
      <image class="search-icon" src="/assets/images/home/<USER>" mode="aspectFit"/>
      <input 
        class="search-input" 
        placeholder="{{placeholder}}" 
        placeholder-class="placeholder"
        value="{{searchText}}"
        bindinput="onSearchInput"
        focus="{{true}}"
        confirm-type="search"
        bindconfirm="onSearch"
      />
      <view class="clear-btn" bindtap="clearSearch" wx:if="{{searchText}}">
        <image class="clear-icon" src="/assets/images/common/clear.png" mode="aspectFit"/>
      </view>
    </view>
    <view class="cancel-btn" bindtap="onCancel">取消</view>
  </view>

  <!-- AI助手 -->
  <view class="ai-helper" bindtap="onTapAI">
    <image class="ai-icon" src="/assets/images/search/robot.png" mode="aspectFit"/>
    <text class="ai-text">试试AI搜索，找到更精准的内容</text>
  </view>

  <!-- 标签页切换 -->
  <view class="tab-section" wx:if="{{searchType === 'all'}}">
    <view class="tab-list">
      <view 
        class="tab-item {{currentTab === 'post' ? 'active' : ''}}" 
        bindtap="onTabChange" 
        data-tab="post"
      >
        帖子
      </view>
      <view 
        class="tab-item {{currentTab === 'institution' ? 'active' : ''}}" 
        bindtap="onTabChange" 
        data-tab="institution"
      >
        机构
      </view>
      <view 
        class="tab-item {{currentTab === 'businessCard' ? 'active' : ''}}" 
        bindtap="onTabChange" 
        data-tab="businessCard"
      >
        名片
      </view>
    </view>
  </view>

  <!-- 分类标签 -->
  <view class="category-section" wx:if="{{categories.length > 0}}">
    <view class="category-list">
      <view 
        class="category-item {{activeCategory === item ? 'active' : ''}}" 
        wx:for="{{categories}}" 
        wx:key="*this"
        bindtap="onTapCategory"
        data-category="{{item}}"
      >
        {{item}}
      </view>
    </view>
  </view>

  <!-- 搜索结果 -->
  <view class="search-results" wx:if="{{searchText && searchResults.length > 0}}">
    <view class="section-title">搜索结果</view>
    
    <!-- 帖子搜索结果 -->
    <view class="post-list" wx:if="{{currentTab === 'post' || searchType === 'post'}}">
      <content-card
        wx:for="{{searchResults}}"
        wx:key="id"
        post="{{item}}"
        themeColor="#F18F01"
      />
    </view>
    
    <!-- 机构搜索结果 -->
    <view class="institution-list" wx:if="{{currentTab === 'institution' || searchType === 'institution'}}">
      <view 
        class="institution-item" 
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="onInstitutionTap"
        data-id="{{item.id}}"
      >
        <image class="institution-avatar" src="{{item.logo || '/assets/images/default-avatar.png'}}" mode="aspectFill"/>
        <view class="institution-info">
          <view class="institution-name">{{item.name}}</view>
          <view class="institution-desc">{{item.description}}</view>
          <view class="institution-location">{{item.address}}</view>
        </view>
      </view>
    </view>
    
    <!-- 名片搜索结果 -->
    <view class="business-card-list" wx:if="{{currentTab === 'businessCard' || searchType === 'businessCard'}}">
      <view 
        class="business-card-item" 
        wx:for="{{searchResults}}" 
        wx:key="id"
        bindtap="onBusinessCardTap"
        data-id="{{item.id}}"
      >
        <image class="card-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"/>
        <view class="card-info">
          <view class="card-name">{{item.fullName}}</view>
          <view class="card-company">{{item.company}}</view>
          <view class="card-position">{{item.jobTitle}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 推荐内容 -->
  <view class="recommend-section" wx:if="{{!searchText || searchResults.length === 0}}">
    <view class="section-title">推荐</view>
    
    <!-- 推荐帖子 -->
    <view class="post-list" wx:if="{{currentTab === 'post' || searchType === 'post'}}">
      <content-card
        wx:for="{{posts}}"
        wx:key="id"
        post="{{item}}"
        themeColor="#F18F01"
      />
    </view>
    
    <!-- 推荐机构 -->
    <view class="institution-list" wx:if="{{currentTab === 'institution' || searchType === 'institution'}}">
      <view 
        class="institution-item" 
        wx:for="{{institutions}}" 
        wx:key="id"
        bindtap="onInstitutionTap"
        data-id="{{item.id}}"
      >
        <image class="institution-avatar" src="{{item.logo || '/assets/images/default-avatar.png'}}" mode="aspectFill"/>
        <view class="institution-info">
          <view class="institution-name">{{item.name}}</view>
          <view class="institution-desc">{{item.description}}</view>
          <view class="institution-location">{{item.address}}</view>
        </view>
      </view>
    </view>
    
    <!-- 推荐名片 -->
    <view class="business-card-list" wx:if="{{currentTab === 'businessCard' || searchType === 'businessCard'}}">
      <view 
        class="business-card-item" 
        wx:for="{{businessCards}}" 
        wx:key="id"
        bindtap="onBusinessCardTap"
        data-id="{{item.id}}"
      >
        <image class="card-avatar" src="{{item.avatar || '/assets/images/default-avatar.png'}}" mode="aspectFill"/>
        <view class="card-info">
          <view class="card-name">{{item.fullName}}</view>
          <view class="card-company">{{item.company}}</view>
          <view class="card-position">{{item.jobTitle}}</view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 无搜索结果 -->
  <view class="no-results" wx:if="{{searchText && searchResults.length === 0 && !loading}}">
    <image class="no-results-icon" src="/assets/images/common/search.png" mode="aspectFit"/>
    <text class="no-results-text">未找到相关结果</text>
  </view>
</view> 