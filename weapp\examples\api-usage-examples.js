/**
 * API使用示例
 * 展示如何在不同场景下使用统一的API配置
 */

// 引入API配置
const apiConfig = require('../config/api.js');
const { request } = require('../utils/request.js');

/**
 * 示例1: 在Store中使用API配置
 */

// stores/postStore.js 示例
const createPostStore = () => {
  const { post: API } = apiConfig;
  
  const getPostList = async (params = {}) => {
    try {
      const response = await request({
        url: API.page,
        method: 'GET',
        data: {
          current: params.current || 1,
          size: params.size || 10,
          ...params
        }
      });
      
      if (response.code === 200) {
        return response.data;
      } else {
        throw new Error(response.msg || '获取帖子列表失败');
      }
    } catch (error) {
      console.error('获取帖子列表失败:', error);
      return { records: [], total: 0 };
    }
  };
  
  const getPostDetail = async (id) => {
    try {
      const response = await request({
        url: `${API.detail}/${id}`,
        method: 'GET'
      });
      
      if (response.code === 200) {
        return response.data;
      } else {
        throw new Error(response.msg || '获取帖子详情失败');
      }
    } catch (error) {
      console.error('获取帖子详情失败:', error);
      return null;
    }
  };
  
  const likePost = async (postId) => {
    try {
      const response = await request({
        url: `${API.like}/${postId}`,
        method: 'POST'
      });
      
      return response.code === 200;
    } catch (error) {
      console.error('点赞帖子失败:', error);
      return false;
    }
  };
  
  return {
    getPostList,
    getPostDetail,
    likePost
  };
};

/**
 * 示例2: 在页面中使用Store
 */

// pages/post-list/post-list.js 示例
const createPostListPage = () => {
  const postStore = createPostStore();
  
  return {
    data: {
      posts: [],
      loading: false,
      hasMore: true,
      currentPage: 1
    },
    
    async onLoad() {
      await this.loadPosts();
    },
    
    async loadPosts(isRefresh = false) {
      if (this.data.loading) return;
      
      this.setData({ loading: true });
      
      try {
        const params = {
          current: isRefresh ? 1 : this.data.currentPage,
          size: 10
        };
        
        const result = await postStore.getPostList(params);
        
        this.setData({
          posts: isRefresh ? result.records : [...this.data.posts, ...result.records],
          hasMore: result.current < result.pages,
          currentPage: result.current + 1,
          loading: false
        });
      } catch (error) {
        this.setData({ loading: false });
        wx.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    },
    
    async onLikePost(e) {
      const postId = e.currentTarget.dataset.id;
      const success = await postStore.likePost(postId);
      
      if (success) {
        wx.showToast({
          title: '点赞成功',
          icon: 'success'
        });
        // 更新本地数据
        this.updatePostLikeStatus(postId);
      } else {
        wx.showToast({
          title: '点赞失败',
          icon: 'none'
        });
      }
    },
    
    updatePostLikeStatus(postId) {
      const posts = this.data.posts.map(post => {
        if (post.id === postId) {
          return {
            ...post,
            isLiked: !post.isLiked,
            likeCount: post.isLiked ? post.likeCount - 1 : post.likeCount + 1
          };
        }
        return post;
      });
      
      this.setData({ posts });
    }
  };
};

/**
 * 示例3: 在组件中使用API配置
 */

// components/user-card/user-card.js 示例
const createUserCardComponent = () => {
  const { user: API } = apiConfig;
  
  return {
    properties: {
      userId: {
        type: String,
        value: ''
      }
    },
    
    data: {
      userInfo: null,
      loading: true
    },
    
    lifetimes: {
      attached() {
        this.loadUserInfo();
      }
    },
    
    observers: {
      'userId': function(userId) {
        if (userId) {
          this.loadUserInfo();
        }
      }
    },
    
    methods: {
      async loadUserInfo() {
        if (!this.data.userId) return;
        
        this.setData({ loading: true });
        
        try {
          const response = await request({
            url: `${API.profile}/${this.data.userId}`,
            method: 'GET'
          });
          
          if (response.code === 200) {
            this.setData({
              userInfo: response.data,
              loading: false
            });
          } else {
            throw new Error(response.msg);
          }
        } catch (error) {
          console.error('加载用户信息失败:', error);
          this.setData({ loading: false });
        }
      }
    }
  };
};

/**
 * 示例4: 批量API调用
 */

const createBatchApiExample = () => {
  const { post: postAPI, user: userAPI, category: categoryAPI } = apiConfig;
  
  const loadPageData = async () => {
    try {
      // 并行请求多个接口
      const [postsResult, userResult, categoriesResult] = await Promise.all([
        request({
          url: postAPI.page,
          method: 'GET',
          data: { current: 1, size: 10 }
        }),
        request({
          url: userAPI.profile,
          method: 'GET'
        }),
        request({
          url: categoryAPI.list,
          method: 'GET'
        })
      ]);
      
      return {
        posts: postsResult.code === 200 ? postsResult.data : { records: [] },
        user: userResult.code === 200 ? userResult.data : null,
        categories: categoriesResult.code === 200 ? categoriesResult.data : []
      };
    } catch (error) {
      console.error('批量加载数据失败:', error);
      return {
        posts: { records: [] },
        user: null,
        categories: []
      };
    }
  };
  
  return { loadPageData };
};

/**
 * 示例5: 动态API调用
 */

const createDynamicApiExample = () => {
  const callApi = async (module, action, params = {}) => {
    const moduleAPI = apiConfig[module];
    if (!moduleAPI || !moduleAPI[action]) {
      throw new Error(`API ${module}.${action} 不存在`);
    }
    
    const url = moduleAPI[action];
    const method = params.method || 'GET';
    delete params.method;
    
    try {
      const response = await request({
        url,
        method,
        data: params
      });
      
      return response;
    } catch (error) {
      console.error(`调用API ${module}.${action} 失败:`, error);
      throw error;
    }
  };
  
  // 使用示例
  const examples = {
    // 获取帖子列表
    async getPostList() {
      return await callApi('post', 'page', {
        current: 1,
        size: 10
      });
    },
    
    // 获取用户信息
    async getUserProfile() {
      return await callApi('user', 'profile');
    },
    
    // 创建帖子
    async createPost(postData) {
      return await callApi('post', 'publish', {
        method: 'POST',
        ...postData
      });
    }
  };
  
  return { callApi, examples };
};

// 导出示例
module.exports = {
  createPostStore,
  createPostListPage,
  createUserCardComponent,
  createBatchApiExample,
  createDynamicApiExample
};
