const { getCategories, getPosts } = require('../../stores/indexStore.js');
const { getInstitutionList } = require('../../stores/institutionStore.js');
const businessCardStore = require('../../stores/businessCardStore.js');


Page({
  data: {
    searchText: '',
    categories: [],
    activeCategory: '全部',
    posts: [],
    institutions: [],
    businessCards: [],
    loading: false,
    page: 1,
    pageSize: 10,
    hasMore: true,
    searchType: 'post', // 搜索类型：post(帖子), institution(机构), businessCard(名片)
    searchResults: [], // 统一的搜索结果
    currentTab: 'post' // 当前选中的标签页
  },

  async onLoad(options) {
    console.log('搜索页面参数:', options);
    
    // 获取搜索类型参数
    const searchType = options.searchType || 'post';
    this.setData({ 
      searchType,
      currentTab: searchType
    });

    // 根据搜索类型设置页面标题和占位符
    this.setPageConfig(searchType);
    
    await this.loadCategories();
    this.loadRecommendData();
  },

  /**
   * 根据搜索类型设置页面配置
   */
  setPageConfig(searchType) {
    let title = '搜索';
    let placeholder = '输入关键词搜索';
    
    switch (searchType) {
      case 'post':
        title = '搜索帖子';
        placeholder = '输入关键词搜索帖子';
        break;
      case 'institution':
        title = '搜索机构';
        placeholder = '输入关键词搜索机构';
        break;
      case 'businessCard':
        title = '搜索名片';
        placeholder = '输入关键词搜索名片';
        break;
    }
    
    // 设置导航栏标题
    wx.setNavigationBarTitle({ title });
    
    // 更新搜索框占位符
    this.setData({ placeholder });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchText: e.detail.value
    });
  },

  // 清除搜索
  clearSearch() {
    this.setData({
      searchText: '',
      searchResults: []
    });
  },

  // 取消搜索
  onCancel() {
    wx.navigateBack();
  },

  // 执行搜索
  async onSearch() {
    if (!this.data.searchText.trim()) {
      return;
    }
    this.setData({ page: 1, hasMore: true });
    await this.loadSearchData(this.data.activeCategory, this.data.searchText);
  },

  // 点击AI助手
  onTapAI() {
    wx.switchTab({ url: '/pages/ai/ai' });
  },

  // 点击分类
  async onTapCategory(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      activeCategory: category,
      page: 1,
      hasMore: true
    });
    await this.loadSearchData(category, this.data.searchText);
  },

  // 切换标签页
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      currentTab: tab,
      page: 1,
      hasMore: true,
      searchResults: []
    });
    this.loadSearchData(this.data.activeCategory, this.data.searchText);
  },

  // 点击机构
  onInstitutionTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/institution-detail/institution-detail?id=${id}`
    });
  },

  // 点击名片
  onBusinessCardTap(e) {
    const id = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pkg_user/pages/user-card/card-detail/card-detail?id=${id}`
    });
  },

  // 加载推荐数据（支持分类和关键词）
  async loadRecommendData(category = '全部', keyword = '') {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    
    try {
      let params = {
        current: this.data.page,
        size: this.data.pageSize
      };
      
      if (category && category !== '全部') {
        params.categoryName = category;
      }
      if (keyword) {
        params.content = keyword;
      }

      let results = [];
      
      switch (this.data.searchType) {
        case 'post':
          results = await getPosts(params);
          this.setData({
            posts: this.data.page === 1 ? results : [...this.data.posts, ...results],
            searchResults: this.data.page === 1 ? results : [...this.data.searchResults, ...results]
          });
          break;
          
        case 'institution':
          results = await getInstitutionList(params);
          this.setData({
            institutions: this.data.page === 1 ? results : [...this.data.institutions, ...results],
            searchResults: this.data.page === 1 ? results : [...this.data.searchResults, ...results]
          });
          break;
          
        case 'businessCard':
          results = await businessCardStore.getBusinessCardList(params);
          this.setData({
            businessCards: this.data.page === 1 ? results : [...this.data.businessCards, ...results],
            searchResults: this.data.page === 1 ? results : [...this.data.searchResults, ...results]
          });
          break;
      }
      
      this.setData({
        hasMore: results.length === this.data.pageSize
      });
    } catch (e) {
      console.error('加载数据失败:', e);
      wx.showToast({ title: '加载失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载搜索数据
  async loadSearchData(category = '全部', keyword = '') {
    if (this.data.loading || !this.data.hasMore) return;
    this.setData({ loading: true });
    
    try {
      let params = {
        current: this.data.page,
        size: this.data.pageSize,
        keyword: keyword
      };
      
      if (category && category !== '全部') {
        params.categoryName = category;
      }

      let results = [];
      
      switch (this.data.currentTab) {
        case 'post':
          results = await getPosts(params);
          break;
          
        case 'institution':
          results = await getInstitutionList(params);
          break;
          
        case 'businessCard':
          results = await businessCardStore.getBusinessCardList(params);
          break;
      }
      
      this.setData({
        searchResults: this.data.page === 1 ? results : [...this.data.searchResults, ...results],
        hasMore: results.length === this.data.pageSize
      });
    } catch (e) {
      console.error('搜索数据失败:', e);
      wx.showToast({ title: '搜索失败', icon: 'none' });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 加载分类
  async loadCategories() {
    try {
      const categories = await getCategories();
      this.setData({
        categories,
        activeCategory: categories[0] || '全部'
      });
    } catch (e) {
      console.error('加载分类失败:', e);
    }
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.setData({ page: 1, hasMore: true });
    if (this.data.searchText.trim()) {
      await this.loadSearchData(this.data.activeCategory, this.data.searchText);
    } else {
      await this.loadRecommendData(this.data.activeCategory, this.data.searchText);
    }
    wx.stopPullDownRefresh();
  },

  // 上拉加载更多
  async onReachBottom() {
    if (!this.data.hasMore || this.data.loading) return;
    this.setData({ page: this.data.page + 1 });
    if (this.data.searchText.trim()) {
      await this.loadSearchData(this.data.activeCategory, this.data.searchText);
    } else {
      await this.loadRecommendData(this.data.activeCategory, this.data.searchText);
    }
  }
}); 