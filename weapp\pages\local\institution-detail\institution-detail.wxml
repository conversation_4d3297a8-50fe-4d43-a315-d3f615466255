<!--机构详情页面-->
<view class="institution-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重试</button>
  </view>

  <!-- 机构详情内容 -->
  <view wx:else class="content-container">
    <!-- 机构头部信息 -->
    <view class="institution-header">
      <view class="header-content">
        <view class="institution-main">
          <view class="institution-name">{{institution.name}}</view>
          <view class="institution-rating">
            <view class="stars">
              <text wx:for="{{[1,2,3,4,5]}}" wx:key="*this"
                    class="star {{item <= institution.rating ? 'active' : ''}}">★</text>
            </view>
            <text class="rating-text">{{institution.rating || 0}}</text>
            <text class="review-count">({{institution.reviewCount || 0}}条评价)</text>
          </view>
          <view class="institution-address">
            <image class="address-icon" src="/assets/images/common/location.png" mode="aspectFit"/>
            <text class="address-text">{{institution.address || '地址未知'}}</text>
          </view>
        </view>

        <view class="action-buttons">
          <button
            class="favorite-btn {{institution.isFavorite ? 'active' : ''}}"
            bindtap="onToggleFavorite"
            disabled="{{favoriteLoading}}"
          >
            <text class="btn-icon">{{institution.isFavorite ? '❤️' : '🤍'}}</text>
            <text class="btn-text">{{institution.isFavorite ? '已收藏' : '收藏'}}</text>
          </button>
        </view>
      </view>
    </view>

    <!-- Tab导航 -->
    <view class="tab-nav">
      <view
        class="tab-item {{currentTab === 'info' ? 'active' : ''}}"
        data-tab="info"
        bindtap="onTabChange"
      >
        机构信息
      </view>
      <view
        class="tab-item {{currentTab === 'posts' ? 'active' : ''}}"
        data-tab="posts"
        bindtap="onTabChange"
      >
        发布的信息
      </view>
      <view
        class="tab-item {{currentTab === 'comments' ? 'active' : ''}}"
        data-tab="comments"
        bindtap="onTabChange"
      >
        评价互动
      </view>
    </view>

    <!-- Tab内容 -->
    <view class="tab-content">
      <!-- 机构信息 -->
      <view wx:if="{{currentTab === 'info'}}" class="info-content">
        <view class="info-section">
          <view class="section-header">
            <text class="section-icon">ℹ️</text>
            <text class="section-title">机构介绍</text>
          </view>
          <view class="section-content">
            <text class="description">{{institution.description || '暂无介绍'}}</text>
          </view>
        </view>

        <view class="info-section">
          <view class="section-header">
            <text class="section-icon">🕒</text>
            <text class="section-title">营业时间</text>
          </view>
          <view class="section-content">
            <text class="business-hours">{{institution.businessHours || '营业时间未知'}}</text>
          </view>
        </view>

        <view class="info-section" wx:if="{{institution.tags && institution.tags.length > 0}}">
          <view class="section-header">
            <text class="section-icon">🏷️</text>
            <text class="section-title">标签</text>
          </view>
          <view class="section-content">
            <view class="tags-container">
              <text wx:for="{{institution.tags}}" wx:key="*this" class="tag-item">{{item}}</text>
            </view>
          </view>
        </view>

        <view class="info-section" wx:if="{{institution.images && institution.images.length > 0}}">
          <view class="section-header">
            <text class="section-icon">📷</text>
            <text class="section-title">机构图片</text>
          </view>
          <view class="section-content">
            <view class="image-gallery">
              <view
                wx:for="{{institution.images}}"
                wx:key="*this"
                class="gallery-item"
                bindtap="onPreviewImage"
                data-url="{{item}}"
                data-urls="{{institution.images}}"
              >
                <image
                  src="{{item}}"
                  mode="aspectFill"
                  class="gallery-image"
                  lazy-load="true"
                />
                <view class="image-overlay">
                  <text class="preview-icon">🔍</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 发布的信息 -->
      <view wx:elif="{{currentTab === 'posts'}}" class="posts-content">
        <!-- 加载状态 -->
        <view wx:if="{{postsLoading}}" class="loading-state">
          <view class="loading-spinner"></view>
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 空状态 -->
        <view wx:elif="{{postList.length === 0}}" class="empty-state">
          <image class="empty-icon" src="/assets/images/common/empty.png" mode="aspectFit"/>
          <text class="empty-text">该机构暂未发布任何信息</text>
        </view>

        <!-- 帖子列表 -->
        <view wx:else class="post-list">
          <view
            wx:for="{{postList}}"
            wx:key="id"
            class="post-item"
            data-post-id="{{item.id}}"
            bindtap="onViewPost"
          >
            <!-- 帖子头部 -->
            <view class="post-header">
              <view class="post-author">
                <image class="author-avatar" src="{{item.avatar}}" mode="aspectFill"/>
                <view class="author-info">
                  <text class="author-name">{{item.author}}</text>
                  <text class="post-time">{{item.createTime}}</text>
                </view>
              </view>
              <view wx:if="{{item.category}}" class="post-category">{{item.category}}</view>
            </view>

            <!-- 帖子内容 -->
            <view class="post-content">
              <view wx:if="{{item.title}}" class="post-title">{{item.title}}</view>
              <view class="post-text">{{item.content}}</view>

              <!-- 帖子图片 -->
              <view wx:if="{{item.images && item.images.length > 0}}" class="post-images">
                <image
                  wx:for="{{item.images}}"
                  wx:for-item="image"
                  wx:key="*this"
                  class="post-image"
                  src="{{image}}"
                  mode="aspectFill"
                  lazy-load="{{true}}"
                />
              </view>
            </view>

            <!-- 帖子操作 -->
            <view class="post-actions">
              <view
                class="action-item {{item.isLiked ? 'liked' : ''}}"
                data-post-id="{{item.id}}"
                data-index="{{index}}"
                bindtap="onLikePost"
                catchtap="onLikePost"
              >
                <image class="action-icon" src="/assets/images/common/like.png" mode="aspectFit"/>
                <text class="action-text">{{item.likeCount || 0}}</text>
              </view>
              <view class="action-item">
                <image class="action-icon" src="/assets/images/common/comment.png" mode="aspectFit"/>
                <text class="action-text">{{item.commentCount || 0}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 评价互动 -->
      <view wx:elif="{{currentTab === 'comments'}}" class="comments-content">
        <!-- 评价统计 -->
        <view class="comment-stats">
          <view class="stats-item">
            <text class="stats-number">{{feedbackList.length}}</text>
            <text class="stats-label">条评价</text>
          </view>
          <button class="add-comment-btn" bindtap="onShowCommentInput">
            <text class="add-comment-text">写评价</text>
          </button>
        </view>

        <!-- 评论列表 -->
        <view class="comment-section">
          <!-- 加载状态 -->
          <view wx:if="{{commentsLoading && feedbackList.length === 0}}" class="loading-state">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>

          <!-- 评论列表 -->
          <block wx:if="{{feedbackList.length > 0}}">
            <block wx:for="{{feedbackList}}" wx:key="id">
              <comment-card
                id="{{item.id}}"
                feedbackId="{{item.id}}"
                avatar="{{item.avatar}}"
                nickname="{{item.nickname}}"
                content="{{item.content}}"
                time="{{item.time}}"
                likes="{{item.likes}}"
                isHelpful="{{item.isHelpful}}"
                replyToUserName="{{item.replyToUserName}}"
                image="{{item.image}}"
                replyCount="{{item.replyCount}}"
                expanded="{{item.expanded}}"
                replies="{{item.replies}}"
                hasMoreReplies="{{item.hasMoreReplies}}"
                allRepliesLoaded="{{item.allRepliesLoaded}}"
                totalReplyCount="{{item.totalReplyCount}}"
                commentId="{{item.id}}"
                institutionId="{{institution.id}}"
                userId="{{item.userId}}"
                formattedContent="{{item.formattedContent}}"
                bind:like="onFeedbackLikeTap"
                bind:reply="onCommentReply"
                bind:toggleReplies="onToggleReplies"
                bind:loadReplies="loadCommentReplies"
                bind:likeReply="onReplyLike"
                bind:loadMoreReplies="onLoadMoreReplies"
              />
            </block>
          </block>

          <!-- 空状态 -->
          <view wx:elif="{{!commentsLoading}}" class="empty-comments">
            <image class="empty-icon" src="/assets/images/common/empty.png" mode="aspectFit"/>
            <text class="empty-text">暂无评价，快来写下第一条评价吧</text>
            <button class="empty-action-btn" bindtap="onShowCommentInput">写评价</button>
          </view>

          <!-- 加载更多 -->
          <view wx:if="{{commentPage.hasMore && feedbackList.length > 0}}" class="load-more" bindtap="loadCommentList">
            <text class="load-more-text">加载更多</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view wx:if="{{!loading && !error}}" class="bottom-actions">
    <button class="action-btn" bindtap="onCallPhone" wx:if="{{institution.phone}}">
      <image class="action-icon" src="/assets/images/common/call.png" mode="aspectFit"/>
      <text class="action-text">电话</text>
    </button>
    <button class="action-btn" bindtap="onViewLocation" wx:if="{{institution.latitude && institution.longitude}}">
      <image class="action-icon" src="/assets/images/common/location.png" mode="aspectFit"/>
      <text class="action-text">位置</text>
    </button>
    <button class="action-btn primary" open-type="share">
      <text class="action-icon">📤</text>
      <text class="action-text">分享</text>
    </button>
  </view>

  <!-- 评论输入组件 -->
  <comment-input
    id="comment-input"
    show="{{showCommentInput}}"
    placeholder="{{replyToComment ? '回复 @' + replyToComment.nickname + ':' : '写下你的评价...'}}"
    institutionId="{{institution.id}}"
    replyToComment="{{replyToComment}}"
    parentId="{{parentCommentId}}"
    bind:submit="onSubmitComment"
    bind:cancel="onHideCommentInput"
  />
</view>