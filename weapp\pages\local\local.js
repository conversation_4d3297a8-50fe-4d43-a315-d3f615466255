// pages/local/local.js
const { getInstitutionList } = require('../../stores/institutionStore.js');
const { getInstitutionTypeList } = require('../../stores/institutionTypeStore.js');
const { getBannersFromAPI } = require('../../stores/indexStore.js');

Page({
  data: {
    // 机构列表数据
    institutionList: [],
    // 轮播图数据
    banners: [],
    // 加载状态
    loading: true,
    // 是否还有更多数据
    hasMore: true,
    // 当前页码
    currentPage: 1,
    // 每页数量
    pageSize: 10,
    // 搜索关键词
    searchKeyword: '',
    // 分类筛选
    selectedCategory: 'all',
    selectedCategoryId: null, // 用于存储真实的分类ID
    categories: [
      { id: 'all', name: '全部', icon: '/assets/images/institution-types/all.png' },
      { id: 'education', name: '学校', icon: '/assets/images/institution-types/education.png' },
    ], // 从接口获取的分类数据，初始化时有默认值
    // 地区信息
    currentLocation: '当前位置',
    // 刷新状态（已禁用）
    refreshing: false,

    // 导航栏相关
    navBarHeight: 88,
    showTabbar: true,
    showStickyCategory: false,
    scrollIntoView: '',

    // 滚动加载优化相关
    scrollHeight: 0,
    scrollViewHeight: 0,
    preloadThreshold: 0.5,  // 预加载阈值（滚动到50%时开始加载）
    isPreloading: false,    // 是否正在预加载
    reachedBottom: false,   // 是否已到达底部

    // 下拉刷新相关
    isRefreshing: false,
    coins: [],

    // 主Tab切换（最新/附近）
    currentTab: 0, // 0: 最新, 1: 附近

    // 首页组件数据
    banners: [],
    quickAccess: [],

    // 页面加载状态标志
    isPageLoaded: false,
    lastScrollTop: 0,

    // 滚动和导航栏相关
    showTabbar: true,
    scrollIntoView: ''
  },

  onLoad() {
    console.log('本地页面加载');
    // 获取custom-nav组件实例
    this.customNav = this.selectComponent('#custom-nav');
    this.initPage();
  },

  onShow() {
    // 显示底部导航栏
    this.setData({ showTabbar: true });

    // 只有在页面已经加载过的情况下才刷新数据（从其他页面返回时）
    if (this.data.isPageLoaded) {
      console.log('从其他页面返回，刷新数据');
      this.refreshData();
    }
  },

  onHide() {
    // 页面隐藏时隐藏底部导航栏
    this.setData({ showTabbar: false });
  },

  // 导航栏准备完成
  onNavReady(e) {
    const navHeight = e.detail.height;
    const menuButtonHeight = e.detail.menuButtonHeight;
    console.log('导航栏信息:', e.detail);
    this.setData({
      navBarHeight: navHeight,
      menuButtonHeight: menuButtonHeight
    });
    this.customNav = this.selectComponent('#custom-nav');

    // 获取滚动容器高度
    this.getScrollViewHeight();
  },

  // 显示抽屉
  onShowDrawer() {
    console.log('显示抽屉');
  },

  // 显示地区选择器
  onRegionPickerShow() {
    console.log('显示地区选择器');
  },

  // 初始化页面
  async initPage() {
    console.log('开始初始化页面...');

    // 设置初始状态，但不设置loading为true，避免阻塞loadInstitutionList
    this.setData({
      isPageLoaded: false
    });

    try {
      // 并行加载基础数据
      await Promise.all([
        this.loadBanners(),
        this.loadInstitutionTypes()
      ]);

      // 初始化时使用 isRefresh=true 来确保能够正常加载
      await this.loadInstitutionList(true);

      // 异步加载位置信息，不阻塞主流程
      this.getCurrentLocation();

      this.setData({
        isPageLoaded: true
      });

      console.log('页面初始化完成');
    } catch (error) {
      this.handleInitError(error);
    }
  },

  // 处理初始化错误
  handleInitError(error) {
    console.error('页面初始化失败:', error);

    this.setData({
      loading: false,
      refreshing: false,
      isRefreshing: false,
      isPageLoaded: true,
      institutionList: [],
      hasMore: false
    });

    wx.showModal({
      title: '加载失败',
      content: `初始化失败: ${error.message || '未知错误'}`,
      showCancel: true,
      cancelText: '稍后重试',
      confirmText: '重新加载',
      success: (res) => {
        if (res.confirm) {
          this.initPage();
        }
      }
    });
  },

  // 加载机构分类
  async loadInstitutionTypes() {
    console.log('开始加载机构分类...');

    try {
      const categories = await getInstitutionTypeList();
      const finalCategories = this.processCategories(categories);

      this.setData({ categories: finalCategories });
      console.log('机构分类加载完成:', finalCategories.length, '个分类');
    } catch (error) {
      console.error('加载机构分类失败:', error);
      this.setData({ categories: this.getDefaultCategories() });
    }
  },

  // 处理分类数据
  processCategories(categories) {
    const validCategories = categories && categories.length > 0 ? categories : [];

    // 确保包含"全部"选项
    if (!validCategories.find(cat => cat.id === 'all')) {
      validCategories.unshift({
        id: 'all',
        name: '全部',
        icon: '/assets/images/institution-types/all.png'
      });
    }

    return validCategories;
  },

  // 获取默认分类数据
  getDefaultCategories() {
    return [
      { id: 'all', name: '全部', icon: '/assets/images/institution-types/all.png' },
      { id: 'education', name: '学校', icon: '/assets/images/institution-types/education.png' },
    ];
  },

  // 获取当前位置（异步执行，不阻塞主流程）
  async getCurrentLocation() {
    try {
      console.log('开始获取位置信息...');
      await this.getLocation();

      // 实际项目中应该根据获取的位置信息设置具体地址
      this.setData({
        currentLocation: '当前位置'
      });
      console.log('位置获取完成');
    } catch (error) {
      console.log('位置获取失败:', error.message);
      this.setData({
        currentLocation: '位置获取失败'
      });
    }
  },

  // 获取位置信息
  getLocation() {
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'gcj02',
        success: resolve,
        fail: reject
      });
    });
  },



  // 加载机构列表
  async loadInstitutionList(isRefresh = false) {
    // 防止重复请求
    if (this.data.loading && !isRefresh) {
      console.log('正在加载中，跳过重复请求');
      return;
    }

    console.log('开始加载机构列表, isRefresh:', isRefresh);

    this.setData({
      loading: true,
      refreshing: isRefresh,
      isPreloading: false
    });

    try {
      const params = this.buildRequestParams(isRefresh);
      const result = await getInstitutionList(params);

      this.updateInstitutionList(result, isRefresh);
      console.log('机构列表加载完成');
    } catch (error) {
      this.handleLoadError(error, isRefresh);
    }
  },

  // 构建请求参数
  buildRequestParams(isRefresh) {
    const params = {
      current: isRefresh ? 1 : this.data.currentPage,
      size: this.data.pageSize,
      sortType: this.data.currentTab === 0 ? 'latest' : 'nearby'
    };

    // 添加筛选条件
    if (this.data.selectedCategory !== 'all' && this.data.selectedCategoryId) {
      params.typeId = this.data.selectedCategoryId;
    }

    if (this.data.searchKeyword) {
      params.name = this.data.searchKeyword;
    }

    console.log('请求参数:', params);
    return params;
  },

  // 更新机构列表数据
  updateInstitutionList(result, isRefresh) {
    const newList = result.records || [];
    
    // 验证数据完整性
    const validList = newList.filter(item => {
      if (!item || !item.id) {
        console.warn('发现无效的机构数据:', item);
        return false;
      }
      return true;
    });
    
    const institutionList = isRefresh ? validList : [...this.data.institutionList, ...validList];
    const hasMore = result.current < result.pages;

    console.log('处理后的机构列表:', validList.map(item => ({
      id: item.id,
      name: item.name,
      address: item.address
    })));

    this.setData({
      institutionList,
      hasMore,
      currentPage: isRefresh ? 2 : this.data.currentPage + 1,
      loading: false,
      isRefreshing: false,
      refreshing: false,
      isPreloading: false,
      reachedBottom: !hasMore
    });

    console.log(`加载完成: 新增${validList.length}条，总计${institutionList.length}条`);
  },

  // 处理加载错误
  handleLoadError(error, isRefresh) {
    console.error('加载机构列表失败:', error);

    this.setData({
      loading: false,
      isRefreshing: false,
      refreshing: false,
      isPreloading: false
    });

    wx.showModal({
      title: '加载失败',
      content: `${error.message || '网络请求失败'}`,
      showCancel: true,
      cancelText: '取消',
      confirmText: '重试',
      success: (res) => {
        if (res.confirm) {
          this.loadInstitutionList(isRefresh);
        }
      }
    });
  },

  // 刷新数据
  async refreshData() {
    this.resetPagination();
    await this.loadInstitutionList(true);
  },

  // 加载更多
  async loadMore() {
    if (!this.data.hasMore || this.data.loading) return;
    await this.loadInstitutionList();
  },

  // 重置分页状态
  resetPagination() {
    this.setData({
      currentPage: 1,
      hasMore: true,
      reachedBottom: false,
      isPreloading: false,
      institutionList: []
    });
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },

  // 执行搜索
  onSearch() {
    this.resetPagination();
    this.loadInstitutionList(true);
  },

  // 主Tab切换（最新/附近）
  switchMainTab(e) {
    const index = e.currentTarget.dataset.index;
    if (index === this.data.currentTab) return;

    this.setData({ currentTab: index });
    this.refreshData();
  },

  // 分类筛选
  switchTab(e) {
    const index = e.currentTarget.dataset.index;
    const category = this.data.categories[index];

    if (category.id === this.data.selectedCategory) return;

    this.setData({
      selectedCategory: category.id,
      selectedCategoryId: category.id === 'all' ? null : category.id
    });

    this.refreshData();
  },

  // 点击机构项
  onInstitutionTap(e) {
    const institution = e.detail.institution;
    console.log('点击机构:', institution);

    // 详细验证机构数据
    if (!institution) {
      return;
    }

    if (!institution.id || institution.id === 'undefined' || institution.id === 'null') {
      console.error('机构ID无效:', institution.id);
      wx.showToast({
        title: '机构ID无效',
        icon: 'none'
      });
      return;
    }

    // 确保ID是字符串或数字
    const institutionId = String(institution.id);
    // 跳转到机构详情页
    wx.navigateTo({
      url: `/pages/local/institution-detail/institution-detail?id=${encodeURIComponent(institutionId)}`,
      success: () => {
        console.log('成功跳转到机构详情页');
      },
      fail: (error) => {
        console.error('跳转失败:', error);
        wx.showModal({
          title: '跳转失败',
          content: '无法打开机构详情页面，请重试',
          showCancel: false,
          confirmText: '确定'
        });
      }
    });
  },

  // 跳转到名片库
  onGoToCardLibrary() {
    console.log('跳转到名片库');
    wx.navigateTo({
      url: '/pages/card-library/card-library'
    });
  },

  // 申请入驻点击
  onApplySettle() {
    console.log('申请入驻点击');

    // 检查登录状态
    if (!this.checkLogin()) {
      return;
    }

    // 跳转到申请入驻页面
    wx.navigateTo({
      url: '/pkg_user/pages/apply-settle/apply-settle',
      fail: () => {
        // 如果页面不存在，显示开发中提示
        wx.showModal({
          title: '申请入驻',
          content: '感谢您的关注！申请入驻功能正在开发中，请稍后再试。\n\n如有紧急需求，请联系客服。',
          showCancel: true,
          cancelText: '知道了',
          confirmText: '联系客服',
          success: (res) => {
            if (res.confirm) {
              // 这里可以跳转到客服页面或拨打客服电话
              wx.makePhoneCall({
                phoneNumber: '************',
                fail: () => {
                  wx.showToast({
                    title: '请手动拨打************',
                    icon: 'none',
                    duration: 3000
                  });
                }
              });
            }
          }
        });
      }
    });
  },

  // 检查登录状态
  checkLogin() {
    // 这里应该检查实际的登录状态
    // 暂时返回true，实际项目中需要实现登录检查逻辑
    return true;
  },

  // 拨打电话
  onCallPhone(e) {
    
    const phone = e.currentTarget.dataset.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone
      });
    }
  },

  // 查看位置
  onViewLocation(e) {
    
    const item = e.currentTarget.dataset.item;
    if (item.latitude && item.longitude) {
      wx.openLocation({
        latitude: parseFloat(item.latitude),
        longitude: parseFloat(item.longitude),
        name: item.name,
        address: item.address
      });
    }
  },

  // 监听页面滚动
  onScroll(e) {
    const { scrollTop, scrollHeight } = e.detail;
    const scrollViewHeight = this.data.scrollViewHeight;

    // 更新滚动相关数据
    this.setData({
      scrollHeight: scrollHeight
    });

    // 分类栏吸顶
    wx.createSelectorQuery().select('#category-section').boundingClientRect(rect => {
      if (rect) {
        const shouldSticky = rect.top <= this.data.navBarHeight;
        if (shouldSticky !== this.data.showStickyCategory) {
          this.setData({ showStickyCategory: shouldSticky });
        }
      }
    }).exec();

    // 控制tabbar显隐
    if (scrollTop > this.data.lastScrollTop + 10) {
      this.setData({ showTabbar: false });
    } else if (scrollTop < this.data.lastScrollTop - 10) {
      this.setData({ showTabbar: true });
    }
    this.data.lastScrollTop = scrollTop;

    // 关键：同步传递给custom-nav组件
    if (this.customNav && this.customNav.handleScroll) {
      this.customNav.handleScroll(scrollTop);
    }

    // 智能预加载逻辑
    this.handleSmartPreload(scrollTop, scrollHeight, scrollViewHeight);
  },

  // 下拉刷新
  async onPullDownRefresh() {
    this.genCoins();
    this.setData({
      isRefreshing: true,
      currentPage: 1,
      hasMore: true,
      institutionList: [],
      reachedBottom: false,
      isPreloading: false
    });

    // 重新加载数据
    await this.loadPageData();

    // 1.5秒后结束刷新状态
    setTimeout(() => {
      this.setData({
        isRefreshing: false
      });
    }, 1500);
  },

  // 上拉加载更多
  onReachBottom() {
    this.loadMore();
  },

  // 加载轮播图数据
  async loadBanners() {
    try {
      console.log('开始加载轮播图数据...');
      const banners = await getBannersFromAPI();

      // 处理轮播图数据，确保有默认数据
      const processedBanners = banners && banners.length > 0 ? banners : this.getDefaultBanners();

      this.setData({ banners: processedBanners });
      console.log('轮播图数据加载完成:', processedBanners.length, '张');
    } catch (error) {
      console.error('加载轮播图失败:', error);
      // 使用默认轮播图数据
      this.setData({ banners: this.getDefaultBanners() });
    }
  },

  // 获取默认轮播图数据
  getDefaultBanners() {
    return [
      {
        id: 1,
        image: '/assets/images/banner.png',
        title: '发现身边好机构',
        subtitle: '找到最适合你的服务',
        link: '',
        type: 'default'
      },
      {
        id: 2,
        image: '/assets/images/banner.png',
        title: '优质服务推荐',
        subtitle: '专业可靠的本地服务',
        link: '',
        type: 'default'
      },
      {
        id: 3,
        image: '/assets/images/banner.png',
        title: '申请入驻',
        subtitle: '展示你的优质服务',
        link: '/pkg_merchant/pages/apply/apply',
        type: 'apply'
      }
    ];
  },

  // 轮播图点击事件
  onBannerTap(e) {
    const { banner } = e.detail;
    console.log('轮播图点击:', banner);

    if (banner.link) {
      if (banner.type === 'apply') {
        // 申请入驻
        wx.navigateTo({
          url: banner.link
        });
      } else if (banner.link.startsWith('http')) {
        // 外部链接
        wx.showModal({
          title: '提示',
          content: '即将跳转到外部链接',
          success: (res) => {
            if (res.confirm) {
              // 这里可以使用 web-view 或其他方式打开外部链接
              console.log('打开外部链接:', banner.link);
            }
          }
        });
      } else {
        // 内部页面
        wx.navigateTo({
          url: banner.link
        });
      }
    }
  },

  // 获取滚动容器高度
  getScrollViewHeight() {
    wx.createSelectorQuery().in(this).select('.main-scroll-view').boundingClientRect(rect => {
      if (rect) {
        this.setData({
          scrollViewHeight: rect.height
        });
        console.log('滚动容器高度:', rect.height);
      }
    }).exec();
  },

  // 智能预加载处理
  handleSmartPreload(scrollTop, scrollHeight, scrollViewHeight) {
    // 如果没有更多数据或正在加载，直接返回
    if (!this.data.hasMore || this.data.loading || this.data.isPreloading) {
      return;
    }

    // 如果滚动容器高度还没获取到，先获取
    if (!scrollViewHeight) {
      this.getScrollViewHeight();
      return;
    }

    // 计算滚动进度
    const maxScrollTop = scrollHeight - scrollViewHeight;
    const scrollProgress = scrollTop / maxScrollTop;

    // 检查是否到达底部
    const isNearBottom = scrollTop + scrollViewHeight >= scrollHeight - 50; // 距离底部50px

    if (isNearBottom && !this.data.reachedBottom) {
      this.setData({ reachedBottom: true });
      console.log('已到达底部，停止滚动');
      return;
    }

    // 当滚动进度超过预加载阈值时，开始预加载
    if (scrollProgress >= this.data.preloadThreshold && !this.data.isPreloading) {
      console.log(`滚动进度: ${(scrollProgress * 100).toFixed(1)}%, 开始预加载下一页`);
      this.preloadNextPage();
    }
  },

  // 预加载下一页数据
  async preloadNextPage() {
    if (this.data.isPreloading || this.data.loading || !this.data.hasMore) {
      return;
    }

    this.setData({ isPreloading: true });

    try {
      console.log('开始预加载下一页数据...');
      await this.loadInstitutionList(false); // 加载更多数据
      console.log('预加载完成');
    } catch (error) {
      console.error('预加载失败:', error);
    } finally {
      this.setData({ isPreloading: false });
    }
  },

  // 生成金币动画
  genCoins() {
    const coins = [];
    for (let i = 0; i < 15; i++) {
      coins.push({
        left: Math.random() * 100,
        size: 40 + Math.random() * 20,
        delay: Math.random() * 2,
        rotate: Math.random() * 360,
        swing: (Math.random() - 0.5) * 20
      });
    }
    this.setData({ coins });
  },

  // 重新加载页面数据
  async loadPageData() {
    try {
      // 并行加载基础数据
      await Promise.all([
        this.loadBanners(),
        this.loadInstitutionTypes()
      ]);

      await this.loadInstitutionList(true);
      console.log('页面数据加载完成');
    } catch (error) {
      console.error('页面数据加载失败:', error);
    }
  }
});
