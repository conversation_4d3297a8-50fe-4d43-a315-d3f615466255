// const BASE_URL = 'https://assistant.duofan.top'; // 替换为您的后端API地址
const BASE_URL = 'http://localhost'; // 替换为您的后端API地址

const { handleLoginExpired } = require('./loginHandler');

// 过滤空值参数的工具函数
const filterEmptyParams = (data) => {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const filtered = {};
  for (const key in data) {
    const value = data[key];

    // 过滤条件：过滤 null、undefined、空字符串
    // 保留：数字0、布尔值false、其他有效值
    if (value !== null && value !== undefined && value !== '') {
      filtered[key] = value;
    }
  }

  return filtered;
};

// 请求拦截器
const requestInterceptor = (config) => {
  const token = wx.getStorageSync('token').value;
  const openId = wx.getStorageSync('openId');

  config.header = {
    ...config.header,
    'Tenant-Id' :"000000",
    'Authorization': `Basic d2VhcHA6c2FkZXF3ZXh6Y2Nhc2Rxd2U=`,
  };

  if (token) {
    config.header = {
      ...config.header,
      'Blade-Auth': `Bearer ${token}`
    };
  }

  // 添加OpenID头
  if (openId) {
    config.header = {
      ...config.header,
      'X-Open-ID': openId
    };
  }

  // 过滤请求参数中的空值
  if (config.data) {
    config.data = filterEmptyParams(config.data);
  }

  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  const { data } = response;
  // 处理token过期
  if (data.code === 401) {
    handleLoginExpired();
    return Promise.reject(new Error('登录已过期，请重新登录'));
  }
  
  return data;
};

// 刷新token
const refreshToken = () => {
  return new Promise((resolve, reject) => {
    const refreshToken = wx.getStorageSync('refreshToken');
    if (!refreshToken) {
      reject(new Error('未找到刷新token'));
      return;
    }

    wx.request({
      url: `${BASE_URL}/blade-auth/token`,
      method: 'POST',
      data: {
        grantType: 'refresh_token',
        refreshToken: refreshToken
      },
      success: (res) => {
        if (res.data.success) {
          const { accessToken, refreshToken } = res.data.data;
          wx.setStorageSync('token', accessToken);
          wx.setStorageSync('refreshToken', refreshToken);
          resolve(accessToken);
        } else {
          reject(new Error(res.data.msg));
        }
      },
      fail: reject
    });
  });
};

// 统一请求方法
const request = (options) => {
  const config = requestInterceptor(options);
  const fullUrl = `${BASE_URL}${config.url}`;
  return new Promise((resolve, reject) => {
    wx.request({
      ...config,
      url: fullUrl,
      success: (res) => {
        try {
          const result = responseInterceptor(res);
          resolve(result);
        } catch (error) {
          console.error('响应拦截器处理失败:', error);
          reject(error);
        }
      },
      fail: (err) => {
        // 增强错误信息
        const enhancedError = new Error(`网络请求失败: ${err.errMsg || '未知错误'}`);
        enhancedError.originalError = err;
        enhancedError.requestUrl = fullUrl;
        enhancedError.requestConfig = config;

        reject(enhancedError);
      }
    });
  });
};

module.exports = {
  request,
  refreshToken
}; 
