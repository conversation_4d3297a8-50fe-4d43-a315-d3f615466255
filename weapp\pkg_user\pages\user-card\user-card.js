// 引入名片管理 Store
const businessCardStore = require('../../../stores/businessCardStore.js');

Page({
  data: {
    // 后端数据
    myCard: null,
    myPosts: [],
    loading: false,
    isFirstLoad: true, // 标记是否是第一次加载

    userCards: []
  },

  onLoad() {
    console.log('用户卡片页面加载', this.data.userCards);
    // 后台加载真实数据
    this.loadRealData();
  },

  onShow() {
    // 页面显示时刷新数据（但避免重复加载）
    // 只有当不是第一次加载时才刷新
    if (!this.data.isFirstLoad) {
      this.loadRealData();
    } else {
      this.setData({ isFirstLoad: false });
    }
  },

  /**
   * 加载真实数据（后台接口）
   */
  async loadRealData() {
    this.setData({ loading: true });

    try {
      // 并行加载我的名片和名片列表
      const [cardResult] = await Promise.all([
        this.loadMyCard()
      ]);

      console.log('我的名片:', cardResult);

    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      this.setData({ loading: false });
    }
  },



  /**
   * 加载我的名片列表
   */
  async loadMyCard() {
    try {
      const result = await businessCardStore.getMyCard();

      if (result.success && result.data) {
        this.setData({
          myCard: result.data
        });

        // 处理名片列表数据
        if (Array.isArray(result.data) && result.data.length > 0) {
          // 转换所有名片为显示格式
          const userCards = result.data.map(card => this.convertToDisplayFormat(card));
          console.log('转换后的名片列表:', userCards);
          this.setData({
            userCards: userCards
          });
          console.log('设置userCards后:', this.data.userCards);
        } else {
          // 如果没有名片数据，设置空数组
          console.log('没有名片数据，设置空数组');
          this.setData({
            userCards: []
          });
        }
      } else {
        // 如果获取失败，设置空数组
        console.log('获取名片失败，设置空数组');
        this.setData({
          userCards: []
        });
      }

      return result;
    } catch (error) {
      console.error('加载名片异常:', error);
      this.setData({
        userCards: []
      });
      return { success: false, message: error.message };
    }
  },


  /**
   * 转换后端数据格式为前端展示格式
   */
  convertToDisplayFormat(backendCard) {
    return {
      cardId: backendCard.id,
      company: backendCard.company || '未填写公司',
      position: backendCard.jobTitle || '未填写职位',
      businessIntro: backendCard.businessProfile || '未填写业务简介',
      name: backendCard.fullName || '未填写姓名',
      gender: backendCard.gender || 0,
      phone: backendCard.phone || '未填写电话',
      address: backendCard.address || '未填写地址',
      email: backendCard.email || '未填写邮箱',
      website: backendCard.website || '未填写网址',
      wechat: backendCard.weixin || '未填写微信',
      avatar: backendCard.avatar || '/assets/images/def-avatar.png',
      logo: '/assets/images/bot-avatar.png',
      qrCode: '/assets/images/bot-avatar.png',
      images: backendCard.images ? backendCard.images.split(',') : [],
      videos: backendCard.video ? [backendCard.video] : [],
      remark: backendCard.description || '暂无备注',
      isPublic: backendCard.isPublic || 0,
      updateTime: backendCard.updateTime || new Date().toLocaleString(),
      auditStatus: backendCard.auditStatus || 0,
      cardType: '个人名片'
    };
  },

  // 处理添加名片事件
  onAddCard(e) {
    const newCard = e.detail;
    console.log('添加新名片:', newCard);

    // 将新名片添加到列表中
    const userCards = [...this.data.userCards, newCard];
    this.setData({
      userCards: userCards
    });

    console.log('名片列表已更新，总数:', userCards.length);
  },

  /**
   * 保存名片到后端
   */
  async saveCardToBackend(cardData) {
    try {
      const result = await businessCardStore.saveCard(cardData);

      if (result.success) {
        console.log('名片保存成功');
        // 重新加载数据
        await this.loadMyCard();
        return result;
      } else {
        console.error('保存名片失败:', result.message);
        return result;
      }
    } catch (error) {
      console.error('保存名片异常:', error);
      return { success: false, message: error.message };
    }
  },

  /**
   * 删除名片
   */
  async deleteCard(cardId) {
    try {
      const result = await businessCardStore.deleteCard(cardId);

      if (result.success) {
        console.log('名片删除成功');
        // 重新加载数据
        await this.loadRealData();
        return result;
      } else {
        console.error('删除名片失败:', result.message);
        return result;
      }
    } catch (error) {
      console.error('删除名片异常:', error);
      return { success: false, message: error.message };
    }
  },

  /**
   * 获取名片详情
   */
  async getCardDetail(cardId) {
    try {
      const result = await businessCardStore.getCardDetail(cardId);

      if (result.success) {
        console.log('获取名片详情成功:', result.data);
        return result;
      } else {
        console.error('获取名片详情失败:', result.message);
        return result;
      }
    } catch (error) {
      console.error('获取名片详情异常:', error);
      return { success: false, message: error.message };
    }
  },

  /**
   * 处理隐藏名片事件
   */
  onHideCard(e) {
    const { index, cardId } = e.detail;
    console.log('隐藏名片:', { index, cardId });

    // 从列表中移除该卡片
    const userCards = [...this.data.userCards];
    userCards.splice(index, 1);
    this.setData({
      userCards: userCards
    });
  },

  /**
   * 处理删除名片事件
   */
  async onDeleteCard(e) {
    const { index, cardId } = e.detail;
    console.log('删除名片:', { index, cardId });

    // 立即从列表中移除该卡片，提供更好的用户体验
    const userCards = [...this.data.userCards];
    userCards.splice(index, 1);
    this.setData({
      userCards: userCards
    });

    // 重新加载我的名片数据以确保数据同步
    await this.loadMyCard();
  },

  /**
   * 刷新名片数据
   */
  async refreshCardList() {
    await this.loadMyCard();
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    await this.loadRealData();
    wx.stopPullDownRefresh();
  }
});
