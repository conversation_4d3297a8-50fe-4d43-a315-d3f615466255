<layout
  title="名片黄页"
  showSearch="{{false}}"
  showLocation="{{false}}"
  background="linear-gradient(135deg, #ff6b6b 0%, #ff8a8a 100%)"
  showBack="{{true}}"
>
</layout>
<!--本地名片库页面-->
<view class="card-library-page">
  <!-- 页面头部 -->
  <!-- <view class="page-header">
    <view class="header-content">
      <view class="header-title">本地名片库</view>
      <view class="header-subtitle">发现优质名片，拓展人脉资源</view>
    </view>
    <view class="header-actions">
      <view class="search-btn" bindtap="onShowSearch">
        <image class="search-icon" src="/assets/images/common/search.png" mode="aspectFit"/>
      </view>
    </view>
  </view> -->

  <!-- 搜索栏 -->
  <view class="search-container">
    <view class="search-box">
      <image class="search-icon" src="/assets/images/common/search.png" mode="aspectFit"/>
      <input 
        class="search-input" 
        placeholder="发现优质名片，拓展人脉资源" 
        placeholder-class="input-placeholder" 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
        focus="{{searchFocus}}"
      />
      <view wx:if="{{searchKeyword}}" class="clear-btn" bindtap="onClearSearch">
        <text class="clear-icon">×</text>
      </view>
    </view>
    <!-- <view class="search-cancel" bindtap="onHideSearch">取消</view> -->
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-item {{selectedFilter === '' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="">
        全部
      </view>
      <view class="filter-item {{selectedFilter === 'nearby' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="nearby">
        附近
      </view>
      <view class="filter-item {{selectedFilter === 'recent' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="recent">
        最新
      </view>
      <view class="filter-item {{selectedFilter === 'popular' ? 'active' : ''}}" bindtap="onFilterTap" data-filter="popular">
        热门
      </view>
    </scroll-view>
    <view class="filter-more" bindtap="onShowMoreFilter">
      <image class="filter-icon" src="/assets/images/common/filter-color-main.png" mode="aspectFit"/>
    </view>
  </view>

  <!-- 名片列表 -->
  <scroll-view 
    class="card-list-scroll" 
    scroll-y="true"
    bindscrolltolower="onReachBottom"
    refresher-enabled="true"
    bindrefresherrefresh="onRefresh"
    refresher-triggered="{{refreshing}}"
  >
    <!-- 加载状态 -->
    <view wx:if="{{loading && cards.length === 0}}" class="loading-container">
      <view class="loading-content">
        <view class="loading-spinner"></view>
        <text class="loading-text">加载中...</text>
      </view>
    </view>

    <!-- 名片列表 -->
    <view wx:elif="{{cards.length > 0}}" class="card-list">
      <view wx:for="{{cards}}" wx:key="id" class="card-item" bindtap="onCardTap" data-card="{{item}}">
        <!-- 名片头部 -->
        <view class="card-header">
          <view class="card-avatar">
            <image 
              class="avatar-image" 
              src="{{item.avatar || '/assets/images/common/default-avatar.png'}}" 
              mode="aspectFill"
            />
          </view>
          <view class="card-info">
            <view class="card-name">{{item.fullName || '未填写'}}</view>
            <view class="card-position">{{item.jobTitle || '未填写'}}</view>
            <view class="card-company">{{item.company || '未填写'}}</view>
          </view>
          <view class="card-actions">
            <view class="action-btn like-btn {{item.isLiked ? 'active' : ''}}" 
                  bindtap="onLikeTap" 
                  data-id="{{item.id}}" 
                  data-index="{{index}}">
              <text class="action-icon">{{item.isLiked ? '❤️' : '🤍'}}</text>
              <text class="action-count">{{item.likeCount || 0}}</text>
            </view>
          </view>
        </view>

        <!-- 名片内容 -->
        <view class="card-content">
          <view wx:if="{{item.businessProfile}}" class="card-profile">
            <text class="profile-text">{{item.businessProfile}}</text>
          </view>
          
          <view class="card-contact">
            <view wx:if="{{item.phone}}" class="contact-item">
              <image class="contact-icon" src="/assets/images/common/call.png" mode="aspectFit"/>
              <text class="contact-text">{{item.phone}}</text>
            </view>
            <view wx:if="{{item.address}}" class="contact-item">
              <image class="contact-icon" src="/assets/images/common/location.png" mode="aspectFit"/>
              <text class="contact-text">{{item.address}}</text>
              <text wx:if="{{item.distance}}" class="distance-text">{{item.distance}}km</text>
            </view>
          </view>
        </view>

        <!-- 名片底部 -->
        <view class="card-footer">
          <view class="card-stats">
            <text class="stat-item">已添加 {{item.favoriteCount || 0}}</text>
            <text class="stat-item">浏览 {{item.viewCount || 0}}</text>
            <text class="stat-time">{{item.createTime}}</text>
          </view>
          <view class="card-operations">
            <button class="operation-btn favorite-btn {{item.isFavorited ? 'active' : ''}}"
                    bindtap="onFavoriteTap"
                    data-id="{{item.id}}"
                    data-index="{{index}}">
              <text class="btn-icon">{{item.isFavorited ? '📱' : '📞'}}</text>
              <text class="btn-text">{{item.isFavorited ? '已添加' : '添加联系人'}}</text>
            </button>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view wx:else class="empty-container">
      <image class="empty-icon" src="/assets/images/common/search.png" mode="aspectFit"/>
      <text class="empty-title">暂无名片数据</text>
      <text class="empty-desc">当前没有找到符合条件的名片</text>
      <view class="empty-action" bindtap="onRefresh">
        <text class="action-text">刷新重试</text>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{cards.length > 0 && !noMore}}" class="load-more">
      <view wx:if="{{loadingMore}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text class="loading-text">加载中...</text>
      </view>
      <text wx:else class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 没有更多 -->
    <view wx:if="{{noMore && cards.length > 0}}" class="no-more">
      <text class="no-more-text">没有更多了</text>
    </view>
  </scroll-view>

  <!-- 添加联系人提示 -->
  <view wx:if="{{showContactTip}}" class="contact-tip">
    <text class="tip-text">正在添加到通讯录...</text>
  </view>

  <!-- 筛选弹窗 -->
  <view wx:if="{{showFilterModal}}" class="modal-overlay" bindtap="onHideFilterModal">
    <view class="filter-modal" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">筛选条件</text>
        <view class="modal-close" bindtap="onHideFilterModal">×</view>
      </view>

      <view class="modal-content">
        <view class="filter-section">
          <text class="section-title">距离范围</text>
          <view class="range-options">
            <view wx:for="{{distanceOptions}}" wx:key="value"
                  class="range-item {{selectedDistance === item.value ? 'active' : ''}}"
                  bindtap="onDistanceSelect"
                  data-value="{{item.value}}">
              {{item.label}}
            </view>
          </view>
        </view>

        <view class="filter-section">
          <text class="section-title">行业分类</text>
          <view class="industry-options">
            <view wx:for="{{industryOptions}}" wx:key="*this"
                  class="industry-item {{selectedIndustry === item ? 'active' : ''}}"
                  bindtap="onIndustrySelect"
                  data-industry="{{item}}">
              {{item}}
            </view>
          </view>
        </view>
      </view>

      <view class="modal-footer">
        <button class="modal-btn reset-btn" bindtap="onResetFilter">重置</button>
        <button class="modal-btn confirm-btn" bindtap="onConfirmFilter">确定</button>
      </view>
    </view>
  </view>
</view>
