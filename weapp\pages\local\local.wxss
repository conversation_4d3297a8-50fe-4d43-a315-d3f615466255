/* pkg_user/pages/local/local.wxss */

/* 页面整体 */
page {
  height: 100vh;
  background: #FF7B7B;
  overflow: hidden; /* 禁止页面整体滚动 */
}

/* 滚动容器 */
.scroll-container {
  position: relative;
  background: #FF7B7B;
  box-sizing: border-box;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  min-height: calc(100vh - 100rpx); /* 确保内容区域有足够高度 */
  background-color: #f5f6f7; /* 内容区域保持浅灰色背景 */
}

/* 轮播图容器样式 */
.banner-container {
  position: relative;
  margin: 0 -20rpx 30rpx -20rpx; /* 负边距让轮播图延伸到屏幕边缘 */
  z-index: 1;
}

/* 轮播图上方渐变效果 - 与导航栏融合 */
.banner-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(to bottom, rgba(255, 107, 107, 0.6), transparent);
  pointer-events: none;
  z-index: 2;
}

/* 轮播图下方渐变效果 */
.banner-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80rpx;
  background: linear-gradient(to top, #f5f5f5, transparent);
  pointer-events: none;
  z-index: 2;
}

/* 禁用组件自带的渐变效果，使用页面级别的渐变 */
.banner-container banner-swiper .top-banner::before,
.banner-container banner-swiper .top-banner::after {
  display: none;
}

/* 确保轮播图内容正确显示 */
.banner-container banner-swiper {
  display: block;
  width: 100%;
}

.refresh-text {
  position: relative;
  z-index: 2;
  color: #fff;
  font-size: 24rpx;
  margin-top: 40rpx;
  opacity: 0.85;
}

/* 下拉刷新圆环 loading 动画样式 */
.refresh-loading {
  width: 100%;
  height: 80rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  background: #ff6b6b; /* 主题色背景 */
  margin-bottom: 10rpx;
}

.circle-loader {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #fff;
  border-top: 4rpx solid #fff;
  border-right: 4rpx solid #fff;
  border-bottom: 4rpx solid #ff6b6b;
  border-left: 4rpx solid #ff6b6b;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 8rpx;
  box-shadow: 0 0 8rpx #ff6b6b33;
  background: transparent;
}

@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

.refresh-text {
  color: #fff;
  font-size: 24rpx;
  opacity: 0.85;
  text-shadow: 0 2rpx 8rpx #ff6b6b33;
}

.refresh-coins {
  width: 100%;
  height: 80rpx;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 10;
  background: #ff6b6b;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  pointer-events: none; /* 不影响下方内容交互 */
}

.coin {
  position: absolute;
  top: -40rpx;
  animation: coinDrop 0.9s cubic-bezier(0.4,1.4,0.6,1) forwards;
  transform: rotate(var(--coin-rotate, 0deg));
}

@keyframes coinDrop {
  0% {
    opacity: 0;
    transform: translateY(-30rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));
  }
  40% {
    opacity: 1;
    transform: translateY(40rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));
  }
  70% {
    transform: translateY(70rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));
  }
  100% {
    opacity: 1;
    transform: translateY(80rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);
  }
}

.refresh-text {
  color: #fff;
  font-size: 24rpx;
  opacity: 0.85;
  text-shadow: 0 2rpx 8rpx #ff6b6b33;
  position: relative;
  z-index: 2;
  margin-top: 40rpx;
}

.refresh-coins-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 160rpx;
  z-index: 9999;
  background: #FF7B7B;
  pointer-events: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
}

.refresh-bottom-area {
  width: 100vw;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-end;
  background: #FF7B7B;
  padding-top: 12rpx;
  padding-bottom: 18rpx;
  margin-bottom: 0;
}

.coin-tray {
  width: 80rpx;
  height: 18rpx;
  background: linear-gradient(90deg, #ffb86b 0%, #ffe066 100%);
  border-radius: 0 0 40rpx 40rpx / 0 0 18rpx 18rpx;
  box-shadow: 0 4rpx 12rpx #ffb86b55;
  margin-bottom: 0;
}

.refresh-text {
  color: #fff;
  font-size: 24rpx;
  opacity: 0.95;
  text-shadow: 0 2rpx 8rpx #ff6b6b33;
  margin-top: 2rpx;
  text-align: center;
}

.coin {
  position: absolute;
  top: 20rpx;
  animation: coinDropFixed 1.1s cubic-bezier(0.4,1.4,0.6,1) forwards;
  transform: rotate(var(--coin-rotate, 0deg));
}

@keyframes coinDropFixed {
  0% {
    opacity: 0;
    transform: translateY(-40rpx) scale(0.7) rotate(var(--coin-rotate, 0deg));
  }
  40% {
    opacity: 1;
    transform: translateY(60rpx) translateX(calc(var(--coin-swing, 0vw) * 0.5)) scale(1.1) rotate(calc(var(--coin-rotate, 0deg) * 0.7));
  }
  70% {
    transform: translateY(110rpx) translateX(var(--coin-swing, 0vw)) scale(0.95) rotate(calc(var(--coin-rotate, 0deg) * 0.3));
  }
  100% {
    opacity: 1;
    transform: translateY(120rpx) translateX(var(--coin-swing, 0vw)) scale(1) rotate(0deg);
  }
} 

/* 吸顶分类栏 */
.sticky-category-bar {
  /* padding: 20rpx 0; */
  position: fixed;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
}

/* 主Tab区域样式 */
.main-tab-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0rpx 12rpx;
  margin-bottom: 24rpx;
}

.main-tabs {
  display: flex;
  align-items: center;
}

.main-tab-item {
  font-size: 32rpx;
  color: #999;
  margin-right: 40rpx;
  padding-bottom: 8rpx;
  position: relative;
  cursor: pointer;
  transition: color 0.2s;
}

.main-tab-item:last-child {
  margin-right: 0;
}

.main-tab-item.active {
  color: #ff6b6b;
  font-weight: bold;
}

.main-tab-item.active::after {
  content: '';
  display: block;
  position: absolute;
  left: 0;
  bottom: 0;
  height: 6rpx;
  width: 100%;
  background: #ff6b6b;
  border-radius: 3rpx;
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* 头部操作按钮区域 */
.header-actions {
  display: flex;
  gap: 16rpx;
}

.action-btn {
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

/* 名片库按钮 */
.card-library-btn {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.card-library-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 0.3);
}

/* 申请入驻按钮 */
.apply-settle-btn {
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
}

.apply-settle-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

/* 卡片区域 - 参考index页面样式 */
.card-section {
  background-color: #F5F5F5;
  border-radius: 24rpx;
  margin: 0; /* 移除外边距，让内容填满 */
  overflow: hidden;
}

.card-section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 分类tab */
.category-tabs-scroll {
  width: 100%;
  white-space: nowrap;
  margin-bottom: 20rpx;
}

.category-tabs {
  display: flex;
  padding: 0 8rpx;
}

.category-tabs.single-tab {
  justify-content: center;
}

.tab-item {
  padding: 10rpx 25rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  margin-right: 25rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
  /* min-width: 150rpx; */
  text-align: center;
  flex-shrink: 0;
}

.tab-item.active {
  color: #fff;
  background-color: #ff6b6b;
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.3);
}



/* 机构列表容器 */
.institutions-container {
  padding: 0;
}

/* 底部状态区域 */
.bottom-status {
  padding: 40rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
}

.loading-more.preloading {
  color: #ff6b6b;
}

.reached-bottom,
.no-more {
  font-size: 26rpx;
  color: #999;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin: 0 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 旧样式已移除，使用组件化设计 */
