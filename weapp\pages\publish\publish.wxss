/* publish.wxss */
.page-container {
  min-height: 100vh;
  background: #f7f8fa;
  padding: 30rpx 30rpx 60rpx;
}

/* 标题区域 */
.title-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 1px 2px rgba(255, 125, 125, 0.06);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.section-stats {
  font-size: 28rpx;
  color: #999999;
}

.more-text {
  font-size: 28rpx;
  color: #FF7D7D;
}

.more-text:active {
  opacity: 0.8;
}

.more-btn {
  font-size: 24rpx;
  color: #FF7D7D;
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  background: #ecf3ff;
}

.title-input-area {
  position: relative;
  margin-bottom: 30rpx;
}

/* 标题输入框样式 */
.title-input {
  width: 100%;
  height: 96rpx;
  padding: 0 32rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
}

.counter {
  position: absolute;
  right: 0;
  top: 0;
  font-size: 24rpx;
  color: #999999;
}

/* 描述区域 */
.desc-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  position: relative;
  box-shadow: 0 1px 2px rgba(255, 125, 125, 0.06);
}

.desc-input-area {
  position: relative;
  margin-bottom: 30rpx;
}

/* 内容输入框样式 */
.desc-input {
  width: 100%;
  height: 280rpx;
  padding: 32rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.6;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
}

/* 图片上传区域 */
.media-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
}

.upload-tip {
  font-size: 24rpx;
  color: #999999;
}

/* 优化图片网格布局 - 一行显示3张 */
.upload-area {
  margin-top: 20rpx;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item, .upload-item {
  width: calc((100% - 32rpx) / 3); /* 一行3张，减去间距 */
  aspect-ratio: 1/1;
  background: #f5f5f5;
  border-radius: 12rpx;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.image-item image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  object-fit: cover;
}

.delete-btn {
  position: absolute;
  top: 8rpx;
  right: 8rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #fff;
  border-radius: 50%;
  width: 40rpx;
  height: 40rpx;
  text-align: center;
  line-height: 40rpx;
  font-size: 24rpx;
  font-weight: bold;
  z-index: 10;
}

.delete-btn:active {
  background: rgba(255, 125, 125, 0.8);
  transform: scale(0.9);
}

.upload-item {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #ddd;
  background: #fafafa;
  transition: all 0.3s ease;
}

.upload-item:active {
  border-color: #FF7D7D;
  background: #fff5f5;
  transform: scale(0.95);
}

.upload-item .plus {
  font-size: 48rpx;
  color: #ccc;
  font-weight: 300;
}

.image-tags {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.5);
  padding: 8rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.image-tags .tag {
  font-size: 18rpx;
  color: #ffffff;
  background: rgba(255, 255, 255, 0.2);
  padding: 4rpx 10rpx;
  border-radius: 4rpx;
}

/* 标签区域 */
.tags-section {
  background: #ffffff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.selected-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.selected-tag {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: #FF7D7D;
  color: #fff;
  border-radius: 24rpx;
  font-size: 26rpx;
  position: relative;
}

.delete-tag {
  margin-left: 8rpx;
  font-size: 24rpx;
  color: #fff;
  opacity: 0.8;
}

.delete-tag:active {
  opacity: 1;
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.tag-item {
  padding: 12rpx 20rpx;
  background: #f5f5f5;
  color: #666;
  border-radius: 24rpx;
  font-size: 26rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.tag-item.selected {
  background: #FF7D7D;
  color: #fff;
  border-color: #FF7D7D;
  transform: scale(1.05);
}

.tag-item:active {
  transform: scale(0.95);
}

/* 发布按钮样式 */
.publish-btn-section {
  padding: 40rpx;
  margin-top: 40rpx;
}

.publish-btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(to right, #FF7D7D, #ff8585);
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 125, 0.3);
  transition: all 0.3s ease;
}

.publish-btn:active {
  opacity: 0.9;
  transform: translateY(2rpx) scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 125, 125, 0.4);
}

/* 联系人信息区域样式 */
.contact-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 1px 2px rgba(255, 125, 125, 0.06);
}

.section-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 24rpx;
}

.contact-type-title {
  margin-top: 32rpx;
}

/* 联系人信息输入框样式 */
.contact-input {
  width: 100%;
  height: 96rpx;
  padding: 0 32rpx;
  font-size: 30rpx;
  color: #333;
  box-sizing: border-box;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
}

.contact-type-group {
  margin-bottom: 24rpx;
}

.radio-group {
  display: flex;
  gap: 48rpx;
}

.radio-label {
  display: flex;
  align-items: center;
  font-size: 30rpx;
  color: #333;
}

/* 自定义单选框样式 */
.radio-label radio {
  margin-right: 12rpx;
  transform: scale(0.9);
}

/* 修改单选框的选中颜色 */
.radio-label radio .wx-radio-input.wx-radio-input-checked {
  background-color: #FF7D7D !important;
  border-color: #FF7D7D !important;
}

/* 修改单选框选中后中间的对勾颜色 */
.radio-label radio .wx-radio-input.wx-radio-input-checked::before {
  color: #fff;
}

/* 修改未选中状态的边框颜色 */
.radio-label radio .wx-radio-input {
  border-color: #ddd;
}

/* 统一输入框placeholder样式 */
.title-input::placeholder,
.desc-input::placeholder,
.contact-input::placeholder {
  color: #999;
  font-size: 30rpx;
}

/* 移除输入框激活状态的背景色变化 */
.title-input:focus,
.desc-input:focus,
.contact-input:focus {
  border-color: #FF7D7D;
}

/* 位置信息区域样式 */
.location-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.location-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 96rpx;
  padding: 0 32rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  transition: border-color 0.3s ease;
}

.location-picker:active {
  border-color: #FF7D7D;
}

.location-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.location-icon {
  width: 32rpx;
  height: 32rpx;
}

.location-text {
  font-size: 30rpx;
  color: #333;
}

.location-text.placeholder {
  color: #999;
}

.arrow-icon {
  width: 32rpx;
  height: 32rpx;
}

.category-tip {
  margin: 24rpx 0 0 0;
  padding: 16rpx 32rpx;
  background: linear-gradient(to right, #fff, #ffeaea 80%);
  border-radius: 12rpx;
  color: #ff6b6b;
  font-size: 28rpx;
  font-weight: 500;
}

.category-select {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 32rpx 16rpx 0 16rpx;
  background: transparent;
}

.form-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(to right, #FF7D7D, #ffb3b3);
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
  border-radius: 44rpx;
  text-align: center;
  box-shadow: 0 4rpx 16rpx #FF7D7D33;
  transition: background 0.2s, transform 0.2s;
  margin-top: 32rpx;
}

.form-btn:active {
  background: linear-gradient(to right, #ffb3b3, #FF7D7D);
  opacity: 0.9;
  transform: scale(0.98);
}

.tag-section {
  margin: 32rpx 0 0 0;
}

.tag-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
}

/* 标签选择弹窗样式 */
.tag-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  position: relative;
  width: 90%;
  max-width: 600rpx;
  max-height: 80vh;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(to right, #FF7D7D, #ff8585);
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
}

.modal-close {
  font-size: 40rpx;
  color: #fff;
  opacity: 0.8;
  padding: 8rpx;
}

.modal-close:active {
  opacity: 1;
  transform: scale(0.9);
}

.modal-body {
  padding: 40rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-body .section-title {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 20rpx;
  margin-top: 32rpx;
}

.modal-body .section-title:first-child {
  margin-top: 0;
}

/* 自定义标签输入区域 */
.custom-tag-section {
  margin-top: 32rpx;
}

.custom-tag-input {
  display: flex;
  gap: 16rpx;
  align-items: center;
}

.tag-input {
  flex: 1;
  height: 72rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  background: #fff;
}

.tag-input:focus {
  border-color: #FF7D7D;
}

.add-tag-btn {
  padding: 16rpx 24rpx;
  background: #FF7D7D;
  color: #fff;
  font-size: 28rpx;
  border-radius: 12rpx;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.add-tag-btn:active {
  background: #ff6b6b;
  transform: scale(0.95);
}

/* 已选标签显示 */
.selected-tags {
  margin-top: 32rpx;
}

.selected-tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 16rpx;
}

.selected-tag {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  background: linear-gradient(to right, #FF7D7D, #ff8585);
  color: #fff;
  border-radius: 24rpx;
  font-size: 26rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 125, 125, 0.3);
  animation: tagSlideIn 0.3s ease-out;
}

@keyframes tagSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20rpx) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.selected-tag .delete-tag {
  margin-left: 12rpx;
  font-size: 24rpx;
  color: #fff;
  opacity: 0.8;
  padding: 4rpx;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.selected-tag .delete-tag:active {
  opacity: 1;
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.2);
}

/* 弹窗底部按钮 */
.modal-footer {
  display: flex;
  gap: 20rpx;
  padding: 32rpx 40rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.cancel-btn:active {
  background: #e5e5e5;
  transform: scale(0.98);
}

.confirm-btn {
  background: linear-gradient(to right, #FF7D7D, #ff8585);
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(255, 125, 125, 0.3);
}

.confirm-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 125, 125, 0.4);
}
