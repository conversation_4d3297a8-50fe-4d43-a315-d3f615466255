<!-- pkg_user/pages/business-card/business-card.wxml -->
<view class="card-container">
  <!-- 顶部搜索栏 -->
  <view class="search-header">
    <view class="search-box">
      <input 
        class="search-input" 
        placeholder="搜索姓名、公司..." 
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        bindconfirm="onSearch"
      />
      <view class="search-btn" bindtap="onSearch">
        <image class="search-icon" src="/assets/images/home/<USER>"></image>
      </view>
    </view>
    
    <!-- 添加名片按钮 -->
    <view class="add-card-btn" bindtap="onAddCard">
      <text class="add-icon">+</text>
    </view>
  </view>

  <!-- 分类筛选 -->
  <view class="category-filter">
    <scroll-view scroll-x class="category-scroll">
      <view class="category-list">
        <view 
          class="category-item {{selectedCategory === item.id ? 'active' : ''}}"
          wx:for="{{categories}}" 
          wx:key="id"
          data-id="{{item.id}}"
          bindtap="onCategoryChange">
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 名片列表 -->
  <view class="card-list">
    <!-- 加载状态 -->
    <view wx:if="{{loading && cardList.length === 0}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text class="loading-text">正在加载名片...</text>
    </view>

    <!-- 空状态 -->
    <view wx:elif="{{!loading && cardList.length === 0}}" class="empty-container">
      <text class="empty-icon">📇</text>
      <text class="empty-text">暂无名片</text>
      <text class="empty-tip">点击右上角添加您的第一名片，让更多人认识您</text>
      <view class="empty-btn" bindtap="onAddCard">
        <text>添加名片</text>
      </view>
    </view>

    <!-- 名片列表项 -->
    <view wx:else>
      <view 
        class="card-item"
        wx:for="{{cardList}}" 
        wx:key="id"
        data-item="{{item}}"
        bindtap="onCardTap">
        
        <!-- 名片头像 -->
        <view class="card-avatar">
          <image 
            wx:if="{{item.avatar}}" 
            src="{{item.avatar}}" 
            class="avatar-image"
            mode="aspectFill"
          />
          <text wx:else class="avatar-placeholder">{{item.name.charAt(0)}}</text>
        </view>
        
        <!-- 名片信息 -->
        <view class="card-info">
          <view class="card-header">
            <text class="card-name">{{item.name}}</text>
            <text class="card-category">{{item.category}}</text>
          </view>
          
          <view class="card-details">
            <text class="card-position">{{item.position}}</text>
            <text class="card-company">{{item.company}}</text>
          </view>
          
          <view class="card-contact">
            <text class="contact-phone">📱 {{item.phone}}</text>
            <text class="contact-email">📧 {{item.email}}</text>
          </view>
        </view>
        
        <!-- 名片操作 -->
        <view class="card-actions">
          <view class="action-time">{{item.createTime}}</view>
          <view class="action-arrow">›</view>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view wx:if="{{hasMore && cardList.length > 0}}" class="load-more">
      <view wx:if="{{loading}}" class="loading-more">
        <view class="loading-spinner small"></view>
        <text>加载中...</text>
      </view>
      <text wx:else class="load-more-text">上拉加载更多</text>
    </view>

    <!-- 没有更多数据 -->
    <view wx:if="{{!hasMore && cardList.length > 0}}" class="no-more">
      <text>📇 已显示全部名片</text>
    </view>
  </view>
</view>
