<!--publish.wxml-->
<layout
  title="{{category}}"
  showSearch="{{false}}"
  showLocation="{{false}}"
  showBack="{{true}}"
  bind:locationChange="onLocationChange"
>
  <view class="page-container">

<!-- 风险提示 -->
 <disclaimer />
    <!-- 描述区域 -->
    <view class="desc-section">
      <view class="section-header">
        <view class="section-title">内容</view>
        <view class="section-stats">
          <text class="stat-item">{{description.length || 0}}/{{maxContentLength}}</text>
        </view>
      </view>
      <view class="desc-input-area">
        <textarea class="desc-input"
                  placeholder="请输入内容描述"
                  placeholder-style="color: #999999"
                  value="{{description}}"
                  bindinput="onDescriptionInput"
                  maxlength="{{maxContentLength}}"/>
      </view>
    </view>


    <!-- 动态表单区域 -->
    <view class="dynamic-form-section" wx:if="{{dynamicFormConfig.children && dynamicFormConfig.children.length}}">
      <view class="dynamic-form-list">
        <dynamic-form-item items="{{dynamicFormConfig.children}}" dynamicFormData="{{dynamicFormData}}" 
        bind:dynamicinput="onDynamicInput" 
        bind:dynamicradiochange="onDynamicInput"
        bind:dynamicselectchange="onDynamicInput"
        bind:dynamicdatechange="onDynamicInput"
        bind:dynamicswitchchange="onDynamicInput"
        bind:dynamiccheckboxchange="onDynamicInput"/>
      </view>
    </view>

    <!-- 联系人信息区域 -->
    <view class="contact-section">
      <!-- 联系人 -->
      <view class="section-title">联系人</view>
      <input class="contact-input" 
             placeholder="请输入联系人姓名" 
             placeholder-style="color: #999999"
             value="{{contactName}}"
             bindinput="onContactNameInput"/>

      <!-- 联系方式 -->
      <view class="section-title contact-type-title">联系方式</view>
      <view class="contact-type-group">
        <radio-group class="radio-group" bindchange="selectContactType">
          <label class="radio-label">
            <radio value="0" checked="{{contactType === '0'}}"/>
            <text>电话</text>
          </label>
          <label class="radio-label">
            <radio value="1" checked="{{contactType === '1'}}"/>
            <text>微信</text>
          </label>
        </radio-group>
      </view>
      <input class="contact-input" 
             placeholder="{{contactType === 'phone' ? '请输入手机号码' : '请输入微信号'}}" 
             placeholder-style="color: #999999"
             value="{{contactNumber}}"
             bindinput="onContactNumberInput"
             type="{{contactType === 'phone' ? 'number' : 'text'}}"/>
    </view>

    <!-- 图片上传区 -->
    <view class="media-section">
      <view class="section-header">
        <view class="section-title">图片/视频</view>
        <view class="section-stats">{{images.length}}/{{maxImageCount}}</view>
      </view>
      <view class="upload-area">
        <!-- 图片预览 - 一行显示3张 -->
        <view class="image-grid">
          <view class="image-item"
            wx:for="{{images}}"
            wx:key="path"
            bindtap="previewImage"
            data-src="{{item.path}}"
          >
            <image src="{{item.path}}" mode="aspectFill"/>
            <view class="delete-btn" bindtap="deleteImage" data-index="{{index}}">×</view>
          </view>
          
          <!-- 上传按钮 -->
          <view
            class="upload-item"
            bindtap="chooseImage"
            wx:if="{{images.length < maxImageCount}}"
          >
            <text class="plus">+</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 位置信息区域 -->
    <view class="location-section">
      <view class="section-title">位置信息</view>
      <view class="location-picker" bindtap="chooseLocation">
        <view class="location-content">
          <image class="location-icon" src="/assets/images/common/location.png" mode="aspectFit"/>
          <text class="location-text">{{location ? location : '选择位置'}}</text>
        </view>
        <image class="arrow-icon" src="/assets/images/common/arrow-right.png" mode="aspectFit"/>
      </view>
    </view>

    <!-- 发布按钮 -->
    <view class="publish-btn-section">
      <view class="publish-btn" bindtap="publishPost">发布</view>
    </view>
  </view>
</layout>

<!-- 标签选择弹窗 -->
<view class="tag-modal" wx:if="{{showTagModal}}">
  <view class="modal-mask" bindtap="hideTagModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">选择标签</text>
      <text class="modal-close" bindtap="hideTagModal">×</text>
    </view>
    
    <view class="modal-body">
      <!-- 快捷标签 -->
      <view class="section-title">快捷标签（最多选择{{maxTagCount}}个）</view>
      <view class="tag-list">
        <view 
          class="tag-item {{selectedTags.indexOf(item) > -1 ? 'selected' : ''}}"
          wx:for="{{tagList}}" 
          wx:key="*this"
          bindtap="onTagTap"
          data-tag="{{item}}"
        >
          {{item}}
        </view>
      </view>

      <!-- 自定义标签输入 -->
      <view class="custom-tag-section">
        <view class="section-title">自定义标签</view>
        <view class="custom-tag-input">
          <input 
            class="tag-input" 
            placeholder="输入自定义标签（最多{{maxTagLength}}字）" 
            value="{{customTag}}"
            bindinput="onCustomTagInput"
            maxlength="{{maxTagLength}}"
          />
          <view class="add-tag-btn" bindtap="addCustomTag">添加</view>
        </view>
      </view>

      <!-- 已选标签显示 -->
      <view class="selected-tags" wx:if="{{selectedTags.length > 0}}">
        <view class="section-title">已选标签</view>
        <view class="selected-tag-list">
          <view class="selected-tag" wx:for="{{selectedTags}}" wx:key="*this">
            {{item}}
            <text class="delete-tag" bindtap="deleteTag" data-index="{{index}}">×</text>
          </view>
        </view>
      </view>
    </view>

    <view class="modal-footer">
      <view class="cancel-btn" bindtap="hideTagModal">取消</view>
      <view class="confirm-btn" bindtap="confirmTagsAndPublish">确认发布</view>
    </view>
  </view>
</view> 