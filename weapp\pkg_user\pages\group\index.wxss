.group-page {
  background: #f7f8fa;
  min-height: 100vh;
}

.scroll-container {
  background: #f7f8fa;
}

.page-content {
  background: #f7f8fa;
  min-height: 100vh;
  padding-bottom: 32rpx;
}
.group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 24rpx 0 24rpx;
}
.group-title {
  font-size: 40rpx;
  font-weight: bold;
  color: #222222;
}
.group-header-actions {
  display: flex;
  gap: 12rpx;
}
.group-header-btn {
  background: #f7f8fa;
  border: none;
  font-size: 32rpx;
  color: #888888;
  margin-left: 8rpx;
}
.group-search-bar {
  display: flex;
  align-items: center;
  margin: 24rpx;
  background: #ffffff;
  border-radius: 40rpx;
  padding: 0 16rpx;
  height: 72rpx;
}
.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
  opacity: 0.5;
}
.search-input {
  flex: 1;
  border: none;
  background: transparent;
  font-size: 24rpx;
  padding: 0;
  color: #888888;
}
.search-btn {
  background: #FF7D7D;
  color: #fff;
  border: none;
  border-radius: 36rpx;
  font-size: 30rpx;
  padding: 0 40rpx;
  margin-left: 12rpx;
  height: 56rpx;
  line-height: 56rpx;
  font-weight: 500;
}
.group-category-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  padding: 0 24rpx;
  margin-bottom: 24rpx;
  gap: 0;
}
.category-item {
  width: 20%;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 32rpx;
  transition: all 0.3s ease;
  position: relative;
  box-sizing: border-box;
}

.category-item.active {
  transform: scale(1.05);
}

.category-item.active .category-icon {
  background: #FF7D7D;
  border-radius: 50%;
  padding: 12rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 125, 125, 0.3);
}

.category-item.active .category-label {
  color: #f7f8fa;
  background: #FF7D7D;
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-weight: bold;
  box-shadow: 0 2rpx 8rpx rgba(255, 125, 125, 0.3);
}
.category-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
  border-radius: 50%;
}
.category-label {
  font-size: 24rpx;
  color: #222222;
  transition: all 0.3s ease;
  text-align: center;
  min-height: 32rpx;
  line-height: 32rpx;
}
.group-list {
  padding: 0 16rpx;
}
.group-card {
  display: flex;
  align-items: center;
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 24rpx;
  padding: 24rpx 16rpx;
}
.group-card-icon {
  width: 96rpx;
  height: 96rpx;
  background: #FF7D7D;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}
.group-card-img {
  width: 64rpx;
  height: 64rpx;
}
.group-card-content {
  flex: 1;
}
.group-card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #222222;
  margin-bottom: 8rpx;
}
.group-card-desc {
  font-size: 26rpx;
  color: #888888;
}
.group-card-btn {
  background: #FF7D7D;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 28rpx;
  padding: 12rpx 32rpx;
  margin-left: 16rpx;
} 
.qr-modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.7);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.qr-modal {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.qr-image {
  width: 520rpx;
  height: 520rpx;
  margin-bottom: 32rpx;
  border-radius: 24rpx;
  background: #fff;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.12);
}
.qr-tip {
  color: #fff;
  font-size: 30rpx;
  margin-bottom: 24rpx;
  text-align: center;
  text-shadow: 0 2rpx 8rpx rgba(0,0,0,0.18);
} 
.group-card-text-icon {
  width: 64rpx;
  height: 64rpx;
  background: #FF7D7D;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  border-radius: 12rpx;
} 
.qr-save-btn {
  background: #FF7D7D;
  color: #fff;
  border: none;
  border-radius: 32rpx;
  font-size: 30rpx;
  padding: 16rpx 64rpx;
  margin-bottom: 12rpx;
} 

.qr-close-btn{
  background: #888888;
  color: #f7f8fa;
  border: none;
  border-radius: 32rpx;
  font-size: 30rpx;
  padding: 16rpx 64rpx;
  margin-bottom: 12rpx;
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 80rpx 0;
}

.loading-text {
  font-size: 28rpx;
  color: #888888;
}

/* 空状态样式 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 120rpx 0;
}

.empty-text {
  font-size: 28rpx;
  color: #888888;
}

/* 加载更多样式 */
.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.load-more-text {
  font-size: 26rpx;
  color: #888888;
}

/* 没有更多数据样式 */
.no-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.no-more-text {
  font-size: 26rpx;
  color: #cccccc;
}
