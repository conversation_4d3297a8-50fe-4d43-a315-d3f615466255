.post-list-scroll {
  background: #f7f7f7;
  min-height: 100vh;
}
.loading-text {
  text-align: center;
  color: #888;
  padding: 32rpx 0;
}
.no-more-text {
  text-align: center;
  color: #bbb;
  padding: 32rpx 0;
}
.my-posts-tabs {
  display: flex;
  background: #fff;
  border-bottom: 2rpx solid #f5f5f5;
  padding: 0 24rpx;
  margin-bottom: 8rpx;
}
.tab-item {
  flex: 1;
  text-align: center;
  font-size: 30rpx;
  color: #888;
  padding: 32rpx 0 18rpx 0;
  position: relative;
}
.tab-item.active {
  color: #ff6b6b;
  font-weight: bold;
}
.tab-item.active::after {
  content: '';
  display: block;
  width: 48rpx;
  height: 6rpx;
  background: #ff6b6b;
  border-radius: 6rpx;
  position: absolute;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
}
.theme-bg {
  background: linear-gradient(to bottom, #ff6b6b, #ff8585) !important;
  min-height: 100vh;
  padding-bottom: 24rpx;
}
.post-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0,0,0,0.06);
  margin: 24rpx 24rpx 0 24rpx;
  overflow: hidden;
  position: relative;
}
.action-mask {
  position: fixed;
  left: 0; right: 0; top: 0; bottom: 0;
  background: rgba(0,0,0,0.35);
  z-index: 1000;
  animation: fadeIn 0.25s;
}
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
.hidden{
  display: none;
}
.action-sheet {
  position: fixed;
  left: 0; right: 0;
  bottom: 0;
  z-index: 1001;
  pointer-events: none;
}
.action-sheet.show {
  pointer-events: auto;
}
.action-sheet-inner {
  background: #fff;
  border-top-left-radius: 32rpx;
  border-top-right-radius: 32rpx;
  box-shadow: 0 -8rpx 32rpx rgba(0,0,0,0.08);
  margin: 0 24rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
  animation: slideUp 0.25s;
}
@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}
.action-btn {
  padding: 36rpx 0;
  text-align: center;
  font-size: 32rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fff;
  transition: background 0.2s;
}
.action-btn:last-child {
  border-bottom: none;
}
.delete { color: #ff4d4f; }
.edit { color: #4C6FFF; }
.move { color: #ffc53d; }
.complete { color: #07c160; }
.cancel {
  color: #888;
  background: #f7f8fa;
  border-radius: 32rpx;
  margin: 24rpx 24rpx 0 24rpx;
  font-weight: bold;
  font-size: 34rpx;
} 