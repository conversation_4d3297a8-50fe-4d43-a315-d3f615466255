# 用户名片页面后端接口接入指南

## 概述

本文档介绍了如何在保持现有UI不变的情况下，为用户名片页面接入后端接口，实现真实数据的获取和管理。

## 实现策略

### 🎯 **核心思路**
- **保持UI不变**：不修改现有的WXML和WXSS文件
- **后台数据加载**：在页面加载时静默获取真实数据
- **数据格式转换**：将后端数据转换为前端展示格式
- **渐进式增强**：逐步用真实数据替换模拟数据

### 📡 **接口集成**
- **名片管理**：获取、保存、删除个人名片
- **帖子查询**：分页查询用户发布的帖子
- **数据同步**：实时同步后端数据到前端展示

## 后端接口

### 🔧 **控制器增强**

在 `WeChatBusinessCardController.java` 中新增了以下接口：

```java
/**
 * 获取当前用户名片
 */
@GetMapping("/my-card")
public R<BusinessCardVO> getMyCard()

/**
 * 获取我的帖子列表
 */
@GetMapping("/my-posts")
public R<IPage<SupPostVO>> getMyPosts(Query query)

/**
 * 分页查询名片列表
 */
@GetMapping("/list")
public R<IPage<BusinessCardVO>> getCardList(BusinessCardVO businessCard, Query query)

/**
 * 获取名片详情
 */
@GetMapping("/detail")
public R<BusinessCardVO> getCardDetail(@RequestParam Long id)
```

### 📊 **数据结构映射**

**后端实体 → 前端展示格式：**
```javascript
// 后端 BusinessCard 字段 → 前端 userCards 字段
{
  id → cardId,
  company → company,
  jobTitle → position,
  businessProfile → businessIntro,
  fullName → name,
  gender → gender,
  phone → phone,
  address → address,
  email → email,
  website → website,
  weixin → wechat,
  avatar → avatar,
  images → images (split by comma),
  video → videos (array),
  description → remark,
  isPublic → isPublic,
  auditStatus → auditStatus
}
```

## 前端实现

### 📱 **页面逻辑增强**

#### 1. 数据初始化
```javascript
Page({
  data: {
    // 后端数据
    myCard: null,
    myPosts: [],
    loading: false,
    
    // 原有的模拟数据（保持UI展示）
    userCards: [/* 原有数据 */]
  },

  onLoad() {
    console.log('用户卡片页面加载', this.data.userCards);
    // 后台加载真实数据
    this.loadRealData();
  }
});
```

#### 2. 数据加载方法
```javascript
/**
 * 加载真实数据（后台接口）
 */
async loadRealData() {
  this.setData({ loading: true });
  
  try {
    // 并行加载我的名片和我的帖子
    const [cardResult, postsResult] = await Promise.all([
      this.loadMyCard(),
      this.loadMyPosts()
    ]);

    console.log('我的名片:', cardResult);
    console.log('我的帖子:', postsResult);
    
  } catch (error) {
    console.error('加载数据失败:', error);
  } finally {
    this.setData({ loading: false });
  }
}
```

#### 3. 名片数据处理
```javascript
/**
 * 加载我的名片
 */
async loadMyCard() {
  try {
    const result = await businessCardStore.getMyCard();
    
    if (result.success && result.data) {
      this.setData({ myCard: result.data });
      
      // 如果有真实名片数据，更新第一个模拟数据用于展示
      if (result.data.id) {
        const realCard = this.convertToDisplayFormat(result.data);
        const userCards = [realCard, ...this.data.userCards.slice(1)];
        this.setData({ userCards });
      }
    }
    
    return result;
  } catch (error) {
    console.error('加载名片异常:', error);
    return { success: false, message: error.message };
  }
}
```

#### 4. 数据格式转换
```javascript
/**
 * 转换后端数据格式为前端展示格式
 */
convertToDisplayFormat(backendCard) {
  return {
    cardId: backendCard.id,
    company: backendCard.company || '未填写公司',
    position: backendCard.jobTitle || '未填写职位',
    businessIntro: backendCard.businessProfile || '未填写业务简介',
    name: backendCard.fullName || '未填写姓名',
    gender: backendCard.gender || 0,
    phone: backendCard.phone || '未填写电话',
    address: backendCard.address || '未填写地址',
    email: backendCard.email || '未填写邮箱',
    website: backendCard.website || '未填写网址',
    wechat: backendCard.weixin || '未填写微信',
    avatar: backendCard.avatar || '/assets/images/def-avatar.png',
    logo: '/assets/images/bot-avatar.png',
    qrCode: '/assets/images/bot-avatar.png',
    images: backendCard.images ? backendCard.images.split(',') : [],
    videos: backendCard.video ? [backendCard.video] : [],
    remark: backendCard.description || '暂无备注',
    isPublic: backendCard.isPublic || 0,
    updateTime: backendCard.updateTime || new Date().toLocaleString(),
    auditStatus: backendCard.auditStatus || 0,
    cardType: '个人名片'
  };
}
```

### 🔄 **Store 管理**

使用现有的 `businessCardStore.js`，主要方法：

```javascript
// 获取我的名片
await businessCardStore.getMyCard()

// 获取我的帖子
await businessCardStore.getMyPosts({ current: 1, size: 10 })

// 保存名片
await businessCardStore.saveCard(cardData)

// 删除名片
await businessCardStore.deleteCard(ids)

// 获取名片详情
await businessCardStore.getCardDetail(id)
```

## 功能特性

### ✅ **已实现功能**

1. **数据获取**
   - ✅ 获取当前用户名片信息
   - ✅ 分页查询用户发布的帖子
   - ✅ 并行加载提高性能

2. **数据管理**
   - ✅ 保存/更新名片信息
   - ✅ 删除名片功能
   - ✅ 获取名片详情

3. **用户体验**
   - ✅ 保持原有UI展示效果
   - ✅ 后台静默加载真实数据
   - ✅ 支持下拉刷新

4. **数据同步**
   - ✅ 真实数据替换模拟数据
   - ✅ 格式转换确保兼容性
   - ✅ 错误处理和日志记录

### 🔄 **数据流程**

```
页面加载 → 显示模拟数据 → 后台加载真实数据 → 转换数据格式 → 更新展示数据
```

### 📝 **使用示例**

#### 1. 获取我的名片和帖子
```javascript
// 页面加载时自动调用
onLoad() {
  this.loadRealData(); // 后台加载真实数据
}

// 下拉刷新时调用
async onPullDownRefresh() {
  await this.loadRealData();
  wx.stopPullDownRefresh();
}
```

#### 2. 保存名片
```javascript
// 调用保存方法
const result = await this.saveCardToBackend({
  company: '公司名称',
  jobTitle: '职位',
  fullName: '姓名',
  // ... 其他字段
});

if (result.success) {
  console.log('保存成功');
}
```

#### 3. 删除名片
```javascript
// 调用删除方法
const result = await this.deleteCard(cardId);

if (result.success) {
  console.log('删除成功');
}
```

## 技术要点

### 🔧 **关键实现**

1. **无侵入式集成**
   - 保持原有UI组件不变
   - 在后台静默加载真实数据
   - 通过数据转换确保兼容性

2. **数据格式兼容**
   - 后端字段映射到前端字段
   - 处理空值和默认值
   - 数组和字符串的转换

3. **性能优化**
   - 并行加载多个接口
   - 避免重复请求
   - 合理的错误处理

4. **用户体验**
   - 页面立即显示模拟数据
   - 后台加载不影响用户操作
   - 数据更新时平滑过渡

## 测试验证

### 🧪 **测试要点**

1. **数据加载测试**
   - [ ] 页面加载时正确获取名片数据
   - [ ] 帖子列表正确显示
   - [ ] 数据格式转换正确

2. **功能测试**
   - [ ] 保存名片功能正常
   - [ ] 删除名片功能正常
   - [ ] 下拉刷新功能正常

3. **兼容性测试**
   - [ ] 原有UI展示正常
   - [ ] 模拟数据和真实数据切换无异常
   - [ ] 组件交互功能正常

## 总结

通过这种无侵入式的接口接入方式，我们成功实现了：

- ✅ **保持UI稳定**：原有界面和交互完全不变
- ✅ **数据真实化**：后台获取真实的名片和帖子数据
- ✅ **功能完整性**：支持名片的增删改查操作
- ✅ **开发规范**：严格遵循Store模式和组件化设计
- ✅ **用户体验**：无感知的数据加载和更新

这种方式既保证了现有功能的稳定性，又为后续的功能扩展提供了良好的基础。
