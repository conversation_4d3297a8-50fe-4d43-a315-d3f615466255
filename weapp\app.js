// app.js
const { checkLoginStatus, refreshToken } = require('./utils/auth');
const { handleLoginExpired } = require('./utils/loginHandler');
const { getStore, setStore } = require('./utils/util');
const { request } = require('./utils/request');
const apiManager = require('./services/apiManager');

App({
  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost/api', // 添加基础API地址
    refreshLock: false,
    refreshInterval: null,
    tokenTime: 2592000, // token 有效期（秒），可根据实际情况调整
    request: request, // 添加全局request方法
    api: apiManager, // 添加API管理器
    isLoginExpiredHandling: false, // 防止重复处理登录过期
  },
  
  onLaunch() {
    // 初始化云开发环境
    if (!wx.cloud) {
      console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    } else {
      wx.cloud.init({
        env: 'cloud1-0gjj2zyo3acba1ed', // 你的云开发环境ID
        traceUser: true
      });
    }

    // 获取用户地理位置信息并保存到缓存
    wx.getLocation({
      type: 'wgs84',
      success(res) {
        const location = {
          latitude: res.latitude,
          longitude: res.longitude
        };
        setStore('userLocation', location);
        console.log('已保存用户位置:', location);
      },
      fail(err) {
        console.error('获取位置失败:', err);
        setStore('userLocation', {
          latitude: 0,
          longitude: 0
        });
      }
    });
    
    // 启动token自动刷新
    this.startTokenRefresh();
    
    // 监听网络状态变化
    this.setupNetworkListener();
  },
  
  // 启动token自动刷新
  startTokenRefresh() {
    // 清除之前的定时器
    if (this.globalData.refreshInterval) {
      clearInterval(this.globalData.refreshInterval);
    }
    
    this.globalData.refreshInterval = setInterval(() => {
      const token = getStore('token') || {};
      const refresh_token = getStore('refreshToken');
      
      if (!refresh_token || !token.value) {
        return;
      }
      
      const now = Date.now();
      const tokenTime = this.globalData.tokenTime * 1000;
      
      // 检查token是否即将过期（提前5分钟刷新）
      if (now - token.datetime >= (tokenTime - 5 * 60 * 1000) && !this.globalData.refreshLock) {
        this.globalData.refreshLock = true;
        console.log('开始自动刷新token...');
        
        refreshToken(refresh_token)
          .then(res => {
            console.log('token自动刷新成功');
            setStore('token', {
              value: res.data.accessToken,
              datetime: Date.now()
            });
            setStore('refresh_token', res.data.refreshToken);
            this.globalData.refreshLock = false;
          })
          .catch(err => {
            console.error('自动刷新token失败', err);
            this.globalData.refreshLock = false;
            
            // 如果刷新失败，可能是登录过期，触发登录过期处理
            if (!this.globalData.isLoginExpiredHandling) {
              this.globalData.isLoginExpiredHandling = true;
              handleLoginExpired().finally(() => {
                this.globalData.isLoginExpiredHandling = false;
              });
            }
          });
      }
    }, 1000 * 60 * 5); // 每5分钟检查一次
  },
  
  // 设置网络状态监听
  setupNetworkListener() {
    wx.onNetworkStatusChange((res) => {
      console.log('网络状态变化:', res);
      if (res.isConnected) {
        // 网络恢复时，检查登录状态
        if (checkLoginStatus()) {
          console.log('网络恢复，检查登录状态正常');
        } else {
          console.log('网络恢复，但登录状态异常');
        }
      } else {
        console.log('网络断开');
      }
    });
  },
  
  // 全局错误处理
  onError(error) {
    console.error('小程序全局错误:', error);
  },
  
  // 小程序显示时
  onShow() {
    // 检查登录状态
    if (checkLoginStatus()) {
      console.log('小程序显示，登录状态正常');
    } else {
      console.log('小程序显示，未登录状态');
    }
  },
  
  // 小程序隐藏时
  onHide() {
    console.log('小程序隐藏');
  }
});
