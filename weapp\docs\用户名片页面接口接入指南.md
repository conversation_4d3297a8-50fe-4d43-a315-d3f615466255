# 用户名片页面接口接入指南

## 概述

本文档介绍了用户名片页面与后端接口的完整接入方案，包括名片管理和我的帖子查询功能，严格按照小程序开发规范实现。

## 功能特性

### ✨ **核心功能**
- **我的名片管理**：查看、创建、编辑、删除个人名片
- **我的帖子查询**：分页查询当前用户发布的所有帖子
- **数据状态管理**：完善的加载、错误、空状态处理
- **下拉刷新**：支持下拉刷新数据
- **上拉加载**：支持分页加载更多帖子

### 🔧 **技术实现**
- **Store 模式**：使用 businessCardStore.js 统一管理数据请求
- **组件化设计**：保留原有 user-card-list 组件
- **响应式布局**：适配不同屏幕尺寸
- **错误处理**：完善的异常处理和用户提示

## 后端接口

### 📡 **新增接口**

#### 1. 获取当前用户名片
```http
GET /blade-chat/card/my-card
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "company": "上海觅知房地产有限公司",
    "jobTitle": "总经理",
    "businessProfile": "专注于房地产开发与销售",
    "fullName": "觅知君",
    "gender": 1,
    "phone": "123 4567 8910",
    "address": "上海市浦东新区秀浦路11号",
    "email": "<EMAIL>",
    "website": "www.51miz.com",
    "weixin": "mizhi001",
    "avatar": "/assets/images/def-avatar.png"
  }
}
```

#### 2. 获取我的帖子列表
```http
GET /blade-chat/card/my-posts?current=1&size=10
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "records": [
      {
        "id": 1,
        "title": "房产投资咨询",
        "content": "提供专业的房产投资建议...",
        "auditStatus": 1,
        "createTime": "2024-01-20 14:30",
        "viewCount": 128,
        "likeCount": 15
      }
    ],
    "total": 25,
    "current": 1,
    "size": 10
  }
}
```

#### 3. 保存或更新名片
```http
POST /blade-chat/card/create
```

**请求体：**
```json
{
  "id": 1,
  "company": "公司名称",
  "jobTitle": "职位",
  "businessProfile": "业务简介",
  "fullName": "姓名",
  "gender": 1,
  "phone": "电话",
  "address": "地址",
  "email": "邮箱",
  "website": "网址",
  "weixin": "微信号",
  "avatar": "头像URL"
}
```

#### 4. 删除名片
```http
POST /blade-chat/card/remove
```

**请求体：**
```json
{
  "ids": "1,2,3"
}
```

#### 5. 获取名片详情
```http
GET /blade-chat/card/detail?id=1
```

#### 6. 分页查询名片列表
```http
GET /blade-chat/card/list?current=1&size=10
```

## 前端实现

### 📁 **文件结构**
```
weapp/pkg_user/pages/user-card/
├── user-card.js          # 页面逻辑
├── user-card.wxml        # 页面结构
├── user-card.wxss        # 页面样式
├── user-card.json        # 页面配置
└── components/
    └── user-card-list/   # 名片列表组件
```

### 🔄 **Store 管理**
```javascript
// weapp/stores/businessCardStore.js
class BusinessCardStore {
  // 获取我的名片
  async getMyCard()
  
  // 获取我的帖子
  async getMyPosts(params)
  
  // 保存名片
  async saveCard(cardData)
  
  // 删除名片
  async deleteCard(ids)
  
  // 获取名片详情
  async getCardDetail(id)
  
  // 获取名片列表
  async getCardList(params)
}
```

### 📱 **页面功能**

#### 1. 数据加载
```javascript
// 页面加载时自动获取数据
onLoad() {
  this.businessCardStore = businessCardStore;
  this.loadPageData();
}

// 并行加载名片和帖子数据
async loadPageData() {
  const [cardResult, postsResult] = await Promise.all([
    this.loadMyCard(),
    this.loadMyPosts(true)
  ]);
}
```

#### 2. 名片管理
```javascript
// 保存名片
async saveCard(cardData) {
  const result = await this.businessCardStore.saveCard(cardData);
  if (result.success) {
    wx.showToast({ title: '保存成功', icon: 'success' });
    await this.loadMyCard();
  }
}

// 删除名片
async deleteCard(cardId) {
  const result = await wx.showModal({
    title: '确认删除',
    content: '确定要删除这张名片吗？'
  });
  
  if (result.confirm) {
    const deleteResult = await this.businessCardStore.deleteCard(cardId);
    if (deleteResult.success) {
      wx.showToast({ title: '删除成功', icon: 'success' });
      await this.loadPageData();
    }
  }
}
```

#### 3. 帖子管理
```javascript
// 分页加载帖子
async loadMyPosts(reset = false) {
  if (reset) {
    this.setData({
      currentPage: 1,
      hasMore: true,
      myPosts: []
    });
  }

  const result = await this.businessCardStore.getMyPosts({
    current: this.data.currentPage,
    size: this.data.pageSize
  });
  
  if (result.success) {
    const newPosts = result.data.records || [];
    const allPosts = reset ? newPosts : [...this.data.myPosts, ...newPosts];
    
    this.setData({
      myPosts: allPosts,
      currentPage: this.data.currentPage + 1,
      hasMore: newPosts.length >= this.data.pageSize
    });
  }
}
```

#### 4. 交互功能
```javascript
// 下拉刷新
async onPullDownRefresh() {
  await this.loadPageData();
  wx.stopPullDownRefresh();
}

// 上拉加载更多
async onReachBottom() {
  if (!this.data.loading && this.data.hasMore) {
    await this.loadMyPosts(false);
  }
}
```

## 页面结构

### 🎨 **WXML 结构**
```xml
<view class="user-card-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 主要内容 -->
  <view wx:else class="main-content">
    <!-- 我的名片区域 -->
    <view class="my-card-section">
      <view class="section-header">
        <text class="section-title">我的名片</text>
        <button class="edit-btn" bindtap="goToEditCard">
          {{myCard && myCard.id ? '编辑' : '创建'}}
        </button>
      </view>
      
      <!-- 名片内容或空状态 -->
      <view wx:if="{{myCard && myCard.id}}" class="card-content">
        <!-- 名片信息展示 -->
      </view>
      <view wx:else class="empty-card">
        <text class="empty-text">还没有创建名片</text>
        <button class="create-btn" bindtap="goToEditCard">立即创建</button>
      </view>
    </view>

    <!-- 我的帖子区域 -->
    <view class="my-posts-section">
      <view class="section-header">
        <text class="section-title">我的帖子 ({{myPosts.length}})</text>
        <button class="publish-btn" bindtap="goToPublishPost">发布</button>
      </view>
      
      <!-- 帖子列表或空状态 -->
      <view wx:if="{{myPosts.length > 0}}" class="posts-list">
        <view wx:for="{{myPosts}}" wx:key="id" class="post-item">
          <!-- 帖子信息展示 -->
        </view>
      </view>
      <view wx:else class="empty-posts">
        <text class="empty-text">还没有发布帖子</text>
        <button class="publish-btn" bindtap="goToPublishPost">发布第一个帖子</button>
      </view>
    </view>
  </view>
</view>
```

## 样式设计

### 🎨 **关键样式**
```css
/* 加载动画 */
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 名片区域 */
.my-card-section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
}

/* 帖子状态 */
.post-status.approved {
  background-color: #e8f5e8;
  color: #4caf50;
}

.post-status.rejected {
  background-color: #ffebee;
  color: #f44336;
}

.post-status.pending {
  background-color: #fff3e0;
  color: #ff9800;
}
```

## 开发规范遵循

### ✅ **规范要点**

1. **Store 模式**
   - 所有接口调用统一在 `businessCardStore.js` 中管理
   - 页面 JS 文件专注于视图交互和生命周期
   - 实现了数据逻辑与视图逻辑的分离

2. **组件化设计**
   - 保留了原有的 `user-card-list` 组件
   - 组件放在页面私有的 `components` 目录下
   - 使用相对路径引用组件

3. **错误处理**
   - 完善的 try-catch 异常处理
   - 用户友好的错误提示
   - 网络请求失败的重试机制

4. **性能优化**
   - 分页加载避免一次性加载大量数据
   - 并行请求提高加载效率
   - 合理的加载状态管理

## 测试验证

### 🧪 **功能测试**

1. **名片管理测试**
   - [ ] 首次进入页面显示"创建名片"
   - [ ] 创建名片后显示名片信息
   - [ ] 编辑名片功能正常
   - [ ] 删除名片需要确认

2. **帖子查询测试**
   - [ ] 正确显示帖子数量
   - [ ] 帖子状态显示正确（待审核/已通过/未通过）
   - [ ] 分页加载功能正常
   - [ ] 空状态显示正确

3. **交互测试**
   - [ ] 下拉刷新功能正常
   - [ ] 上拉加载更多功能正常
   - [ ] 跳转到编辑页面
   - [ ] 跳转到发布帖子页面

4. **异常处理测试**
   - [ ] 网络异常时显示错误信息
   - [ ] 重试按钮功能正常
   - [ ] 加载状态显示正确

## 总结

通过本次接入，用户名片页面实现了：

- ✅ **完整的名片管理功能**：增删改查一应俱全
- ✅ **我的帖子查询功能**：支持分页和状态显示
- ✅ **规范的代码结构**：严格遵循开发规范
- ✅ **良好的用户体验**：加载状态、错误处理、交互反馈
- ✅ **可维护的架构**：Store 模式、组件化设计

这个实现为后续的功能扩展奠定了良好的基础，同时保持了代码的可读性和可维护性。
