.favorite-tags-card {
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(255,107,107,0.08);
  padding: 24rpx 0 0 0;
  margin-bottom: 32rpx;
}
.favorite-tags-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32rpx 24rpx 32rpx;
}
.favorite-tags-title {
  font-size: 30rpx;
  font-weight: bold;
  color: #222;
}
.favorite-tags-more {
  font-size: 24rpx;
  color: #ff6b6b;
  cursor: pointer;
}
.favorite-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx 20rpx;
  padding: 0 32rpx 24rpx 32rpx;
}
.favorite-tag {
  font-size: 24rpx;
  padding: 8rpx 28rpx;
  border-radius: 20rpx;
  background: #f5f5f5;
  color: #666;
  transition: all 0.2s;
}
.favorite-tag.selected {
  background: #ff6b6b;
  color: #fff;
}

/* 空状态样式 */
.favorite-tags-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 32rpx 40rpx 32rpx;
  text-align: center;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 12rpx;
  font-weight: 500;
}

.empty-desc {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
} 