<view class="post-card">
  <view class="post-image-wrap" wx:if="{{imageList.length > 0}}">
    <image class="post-image" src="{{imageList[0]}}" mode="aspectFill" catchtap="previewImage" data-current="{{imageList[0]}}" />
    <view class="post-more-btn" catchtap="onTapMore">···</view>
  </view>
  <view class="post-content-wrap">
    <text class="meta-status {{post.status === 1 ? 'draft' : 'published'}}">{{post.status === 1 ? '草稿箱' : '已发布'}}</text>
    <view class="post-title">{{post.categoryName}}</view>
    <view class="post-desc">{{post.content}}</view>
    <view class="post-meta">
      <text class="meta-time">{{post.createTime}}</text>
      <view class="meta-data">
        <view class="meta-item">
          <image class="meta-icon" src="/assets/images/common/view.png" mode="aspectFit"></image>
          <text>{{post.viewCount}}</text>
        </view>
        <view class="meta-item">
          <image class="meta-icon" src="/assets/images/common/like.png" mode="aspectFit"></image>
          <text>{{post.favoriteCount}}</text>
        </view>
      </view>
      <!-- <text class="meta-view"><image class="meta-icon" src="/assets/images/common/view.png" />{{post.viewCount || 0}}</text>
      <text class="meta-like"><image class="meta-icon" src="/assets/images/common/like.png" />{{post.likeCount || 0}}</text> --> 
    </view>
  </view>
</view> 