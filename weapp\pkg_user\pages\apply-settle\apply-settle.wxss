/* pkg_user/pages/apply-settle/apply-settle.wxss */

/* 使用layout组件，移除容器样式 */

.apply-header {
  background: linear-gradient(135deg, #FF8181, #ff8585);
  padding: 20rpx 30rpx 40rpx 30rpx;
  color: #fff;
  position: relative;
  overflow: hidden;
}

.apply-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20rpx;
  background: linear-gradient(to bottom, rgba(255, 129, 129, 0.8), rgba(255, 129, 129, 1));
}

/* 装饰元素 */
.header-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 4s ease-in-out infinite;
}

.circle-1 {
  width: 100rpx;
  height: 100rpx;
  top: 30rpx;
  right: 50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 60rpx;
  height: 60rpx;
  bottom: 30rpx;
  left: 80rpx;
  animation-delay: 2s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15rpx) rotate(180deg); }
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.header-icon {
  font-size: 50rpx;
  display: block;
  margin-bottom: 16rpx;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.header-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.header-desc {
  font-size: 28rpx;
  color: #fff;
  opacity: 0.95;
  margin-bottom: 24rpx;
}

/* 优势展示 */
.header-benefits {
  display: flex;
  justify-content: center;
  gap: 32rpx;
  margin-top: 20rpx;
}

.benefit-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.95;
}

.benefit-icon {
  font-size: 28rpx;
  margin-bottom: 6rpx;
  display: block;
}

.benefit-text {
  font-size: 20rpx;
  font-weight: 500;
  text-align: center;
}

.form-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}

.form-item {
  margin-bottom: 40rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 16rpx;
}

.form-input {
  width: 100%;
  height: 80rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.form-input:focus {
  background: #fff;
  border: 2rpx solid #ff6b6b;
}

.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
  line-height: 1.5;
}

.form-textarea:focus {
  background: #fff;
  border: 2rpx solid #ff6b6b;
}

.submit-section {
  background: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
}

.submit-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 32rpx;
  font-weight: 600;
  border-radius: 44rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.submit-btn:active {
  transform: scale(0.98);
}

.submit-btn.submitting {
  background: #ccc;
  box-shadow: none;
}

.submit-btn::after {
  display: none;
}

.tips {
  margin-top: 20rpx;
  text-align: center;
}

.tips-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
}

/* 其他合作方式 */
.other-cooperation {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-top: 20rpx;
}

.other-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.cooperation-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
  transition: all 0.3s ease;
}

.cooperation-btn:active {
  background: #e9ecef;
  transform: scale(0.98);
}

.cooperation-text {
  font-size: 26rpx;
  color: #495057;
  font-weight: 500;
}

.cooperation-arrow {
  font-size: 28rpx;
  color: #adb5bd;
}

/* 新增样式 */
.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin: 40rpx 0 20rpx 0;
  padding-left: 20rpx;
  border-left: 4rpx solid #FF8181;
}

.form-picker {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid #e9ecef;
}

.picker-text {
  font-size: 28rpx;
  color: #333;
}

.picker-text.placeholder {
  color: #999;
}

.picker-arrow {
  font-size: 24rpx;
  color: #999;
  transform: rotate(90deg);
}

.upload-section {
  margin-top: 20rpx;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 160rpx;
  background: #f8f9fa;
  border: 2rpx dashed #dee2e6;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.upload-btn:active {
  background: #e9ecef;
}

.upload-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}

.upload-text {
  font-size: 26rpx;
  color: #666;
}

.upload-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
}

.uploaded-image {
  width: 200rpx;
  height: 150rpx;
  border-radius: 8rpx;
  overflow: hidden;
  margin-top: 20rpx;
}

.uploaded-image image {
  width: 100%;
  height: 100%;
}
