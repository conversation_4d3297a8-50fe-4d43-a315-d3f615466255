<!-- pkg_user/pages/apply-settle/apply-settle.wxml -->
<layout
  title="申请入驻"
  show-back="{{true}}"
  background="linear-gradient(to bottom, #FF8181, #ff8585)"
  text-color="#ffffff">

  <!-- 顶部介绍 -->
  <view class="apply-header">
    <!-- 装饰元素 -->
    <view class="header-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
    </view>

    <view class="header-content">
      <view class="header-icon">🏢</view>
      <text class="header-title">申请入驻</text>
      <text class="header-desc">加入我们，共同服务本地用户</text>

      <!-- 优势展示 -->
      <view class="header-benefits">
        <view class="benefit-item">
          <text class="benefit-icon">✨</text>
          <text class="benefit-text">免费入驻</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">📈</text>
          <text class="benefit-text">流量扶持</text>
        </view>
        <view class="benefit-item">
          <text class="benefit-icon">🎯</text>
          <text class="benefit-text">精准推广</text>
        </view>
      </view>
    </view>
  </view>

  <view class="form-section">
    <!-- 基本信息 -->
    <view class="section-title">基本信息</view>

    <view class="form-item">
      <text class="form-label">机构名称 *</text>
      <input
        class="form-input"
        placeholder="请输入机构名称"
        value="{{formData.name}}"
        data-field="name"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">机构分类 *</text>
      <view class="form-picker" bindtap="onSelectType">
        <text class="picker-text {{formData.typeId ? '' : 'placeholder'}}">
          {{selectedTypeName || '请选择机构分类'}}
        </text>
        <text class="picker-arrow">></text>
      </view>
    </view>

    <view class="form-item">
      <text class="form-label">联系人 *</text>
      <input
        class="form-input"
        placeholder="请输入联系人姓名"
        value="{{formData.contactPerson}}"
        data-field="contactPerson"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">联系电话 *</text>
      <input
        class="form-input"
        placeholder="请输入联系电话"
        type="number"
        value="{{formData.phone}}"
        data-field="phone"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">电子邮箱</text>
      <input
        class="form-input"
        placeholder="请输入电子邮箱"
        value="{{formData.email}}"
        data-field="email"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">微信号</text>
      <input
        class="form-input"
        placeholder="请输入微信号"
        value="{{formData.wechat}}"
        data-field="wechat"
        bindinput="onInputChange"
      />
    </view>

    <!-- 证照信息 -->
    <view class="section-title">证照信息</view>

    <view class="form-item">
      <text class="form-label">营业执照号码 *</text>
      <input
        class="form-input"
        placeholder="请输入营业执照号码"
        value="{{formData.licenseNo}}"
        data-field="licenseNo"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">营业执照照片</text>
      <view class="upload-section">
        <view class="upload-btn" bindtap="onUploadLicense">
          <text class="upload-icon">📷</text>
          <text class="upload-text">上传营业执照</text>
        </view>
        <view wx:if="{{formData.licenseImage}}" class="uploaded-image">
          <image src="{{formData.licenseImage}}" mode="aspectFill"></image>
        </view>
      </view>
    </view>

    <view class="form-item">
      <text class="form-label">法人代表姓名 *</text>
      <input
        class="form-input"
        placeholder="请输入法人代表姓名"
        value="{{formData.legalPerson}}"
        data-field="legalPerson"
        bindinput="onInputChange"
      />
    </view>

    <!-- 地址信息 -->
    <view class="section-title">地址信息</view>

    <view class="form-item">
      <text class="form-label">省份</text>
      <input
        class="form-input"
        placeholder="请输入省份"
        value="{{formData.province}}"
        data-field="province"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">城市</text>
      <input
        class="form-input"
        placeholder="请输入城市"
        value="{{formData.city}}"
        data-field="city"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">区县</text>
      <input
        class="form-input"
        placeholder="请输入区县"
        value="{{formData.district}}"
        data-field="district"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">详细地址 *</text>
      <input
        class="form-input"
        placeholder="请输入详细地址"
        value="{{formData.detailAddress}}"
        data-field="detailAddress"
        bindinput="onInputChange"
      />
    </view>

    <!-- 服务信息 -->
    <view class="section-title">服务信息</view>

    <view class="form-item">
      <text class="form-label">机构介绍</text>
      <textarea
        class="form-textarea"
        placeholder="请介绍您的机构特色和服务"
        value="{{formData.description}}"
        data-field="description"
        bindinput="onInputChange"
        maxlength="500"
      />
    </view>

    <view class="form-item">
      <text class="form-label">特色服务</text>
      <input
        class="form-input"
        placeholder="请输入特色服务"
        value="{{formData.specialServices}}"
        data-field="specialServices"
        bindinput="onInputChange"
      />
    </view>

    <view class="form-item">
      <text class="form-label">商家图片</text>
      <view class="upload-section">
        <view class="upload-btn" bindtap="onUploadImages">
          <text class="upload-icon">🖼️</text>
          <text class="upload-text">上传商家图片</text>
        </view>
        <text class="upload-tip">最多可上传9张图片</text>
      </view>
    </view>
  </view>

  <view class="submit-section">
    <button 
      class="submit-btn {{submitting ? 'submitting' : ''}}" 
      bindtap="onSubmit"
      disabled="{{submitting}}"
    >
      {{submitting ? '提交中...' : '提交申请'}}
    </button>
    
    <view class="tips">
      <text class="tips-text">提交后我们将在3个工作日内审核并联系您</text>
    </view>
  </view>
</layout>
