# 数据操作功能使用指南

## 概述

本文档介绍如何在小程序中使用新的数据操作功能，包括点赞、收藏、分享、浏览等用户行为的处理。

## 功能特性

### ✨ 主要功能
- **状态切换**：支持点赞/取消点赞、收藏/取消收藏的切换操作
- **状态检查**：可以检查用户当前的点赞和收藏状态
- **行为记录**：记录用户的分享和浏览行为
- **统一接口**：提供统一的API接口，支持多种数据类型
- **错误处理**：完善的错误处理和用户反馈机制

### 🎯 设计原则
- **符合小程序开发规范**：遵循项目的开发规范，使用 Store 模式管理数据
- **用户体验优先**：提供流畅的交互体验和及时的状态反馈
- **性能优化**：支持批量操作和状态缓存
- **向后兼容**：保持与现有代码的兼容性

## 快速开始

### 1. 引入数据操作 Store

```javascript
// 在页面 JS 文件中引入
const { 
  toggleLike, 
  toggleFavorite, 
  checkUserStatus, 
  recordView,
  recordShare,
  DATA_TYPES 
} = require('../../stores/dataOperateStore.js');
```

### 2. 基本使用示例

```javascript
Page({
  data: {
    postId: '',
    isLiked: false,
    isFavorited: false,
    likeLoading: false,
    favoriteLoading: false
  },

  async onLoad(options) {
    const { id } = options;
    this.setData({ postId: id });
    
    // 加载用户状态
    await this.loadUserStatus();
  },

  // 加载用户状态
  async loadUserStatus() {
    const { postId } = this.data;
    const userStatus = await checkUserStatus(postId, DATA_TYPES.POST);
    
    this.setData({
      isLiked: userStatus.isLiked,
      isFavorited: userStatus.isFavorited
    });
  },

  // 点赞操作
  async onLikeTap() {
    const { postId, likeLoading } = this.data;
    
    if (likeLoading) return;
    
    this.setData({ likeLoading: true });
    
    try {
      const result = await toggleLike(postId, DATA_TYPES.POST);
      
      if (result.success) {
        this.setData({ isLiked: result.isLiked });
        wx.showToast({
          title: result.message,
          icon: 'success'
        });
      } else {
        wx.showToast({
          title: result.message,
          icon: 'none'
        });
      }
    } catch (error) {
      wx.showToast({
        title: '操作失败',
        icon: 'none'
      });
    } finally {
      this.setData({ likeLoading: false });
    }
  }
});
```

## API 参考

### 核心方法

#### toggleLike(id, type)
切换点赞状态

**参数：**
- `id` (string|number): 目标ID
- `type` (string): 数据类型，可选值：'post', 'institution', 'comment'

**返回值：**
```javascript
{
  success: boolean,
  isLiked: boolean,
  message: string,
  action: 'like' | 'unlike'
}
```

#### toggleFavorite(id, type)
切换收藏状态

**参数：**
- `id` (string|number): 目标ID  
- `type` (string): 数据类型

**返回值：**
```javascript
{
  success: boolean,
  isFavorited: boolean,
  message: string,
  action: 'favorite' | 'unfavorite'
}
```

#### checkUserStatus(id, type)
批量检查用户状态

**参数：**
- `id` (string|number): 目标ID
- `type` (string): 数据类型

**返回值：**
```javascript
{
  isLiked: boolean,
  isFavorited: boolean
}
```

#### recordView(id, type)
记录浏览行为

#### recordShare(id, type)
记录分享行为

### 数据类型常量

```javascript
const DATA_TYPES = {
  POST: 'post',           // 帖子
  INSTITUTION: 'institution', // 机构
  COMMENT: 'comment'      // 评论
};
```

## 组件集成

### Stats Bar 组件

更新后的 stats-bar 组件支持点赞交互：

```xml
<stats-bar 
  views="{{post.views}}" 
  likes="{{post.likes}}" 
  comments="{{post.comments}}" 
  isLiked="{{isLiked}}"
  likeLoading="{{likeLoading}}"
  bind:like="onLikeTap"
  bind:favorite="onFavoriteTap"
  bind:more="onMoreTap" 
/>
```

### 操作按钮

```xml
<view class="action-btns">
  <button 
    class="btn-like {{isLiked ? 'liked' : ''}}" 
    bindtap="onLikeTap"
    disabled="{{likeLoading}}"
  >
    <image 
      class="btn-icon" 
      src="{{isLiked ? '/assets/images/detail/heart-filled.png' : '/assets/images/detail/heart-icon.png'}}" 
    />
    {{isLiked ? '已点赞' : '点赞'}}
  </button>
</view>
```

## 最佳实践

### 1. 状态管理
- 在页面加载时检查用户状态
- 使用本地状态进行UI更新
- 操作失败时回滚状态

### 2. 用户体验
- 提供加载状态指示
- 防止重复点击
- 及时的操作反馈

### 3. 错误处理
- 网络错误的优雅降级
- 用户友好的错误提示
- 操作失败的重试机制

### 4. 性能优化
- 异步记录浏览行为
- 批量检查用户状态
- 合理使用缓存

## 注意事项

1. **网络依赖**：所有操作都依赖网络请求，需要处理网络异常情况
2. **用户认证**：确保用户已登录，否则操作会失败
3. **数据一致性**：操作成功后及时更新本地状态
4. **性能考虑**：避免频繁的状态检查，合理使用缓存

## 故障排除

### 常见问题

**Q: 点赞操作没有反应？**
A: 检查网络连接和用户登录状态，查看控制台错误信息。

**Q: 状态显示不正确？**
A: 确保在页面加载时调用了 `checkUserStatus` 方法。

**Q: 操作后状态没有更新？**
A: 检查操作成功后是否正确更新了本地状态。

### 调试技巧

1. 开启控制台日志查看详细信息
2. 检查网络请求的响应状态
3. 验证传递的参数是否正确
4. 确认后端接口是否正常工作

## 更新日志

### v1.0.0 (2025-07-31)
- 初始版本发布
- 支持点赞、收藏、分享、浏览功能
- 提供统一的API接口
- 完善的错误处理机制
