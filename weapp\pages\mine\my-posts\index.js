const { request } = require('../../../utils/request');

Page({
  data: {
    posts: [],
    page: 1,
    pageSize: 10,
    loading: false,
    noMore: false,
    tab: 'all',
    showActionMenu: false, // 必须为 false
    currentPostId: null
  },
  onLoad() {
    this.loadPosts();
  },
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ tab, page: 1, posts: [], noMore: false }, () => {
      this.loadPosts();
    });
  },
  async loadPosts() {
    if (this.data.loading || this.data.noMore) return;
    this.setData({ loading: true });
    try {
      const res = await request({
        url: '/blade-chat/post/my',
        method: 'GET',
        data: {
          page: this.data.page,
          size: this.data.pageSize,
          status: this.data.tab === 'all' ? '' : (this.data.tab === 'published' ? 'published' : 'draft')
        }
      });
      if (res.code === 200) {
        const newPosts = res.data.records || [];
        const total = Number(res.data.total || 0);
        const allPosts = this.data.page === 1 ? newPosts : this.data.posts.concat(newPosts);
        // 判断已加载数量是否大于等于总数
        const noMore = allPosts.length >= total;
        this.setData({
          posts: allPosts,
          noMore
        });
      }
    } finally {
      this.setData({ loading: false });
    }
  },
  onReachBottom() {
    if (!this.data.noMore) {
      this.setData({ page: this.data.page + 1 }, () => {
        this.loadPosts();
      });
    }
  },
  onEditPost(e) {
    const id = e.detail.id;
    wx.navigateTo({ url: `/pages/publish/form?id=${id}` });
  },
  onMore(e) {
    this.setData({
      showActionMenu: true,
      currentPostId: e.detail.id
    });
  },
  hideActionMenu() {
    this.setData({ showActionMenu: false, currentPostId: null });
  },
  onDeletePostMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onDeletePost({ detail: { id } });
  },
  onEditPostMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onEditPost({ detail: { id } });
  },
  onMoveDraftMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onMoveDraft({ detail: { id } });
  },
  onCompletedPostMenu() {
    const id = this.data.currentPostId;
    this.hideActionMenu();
    this.onCompletedPost({ detail: { id, completed: false } });
  },
  async onDeletePost(e) {
    const id = e.detail.id;
    try {
      wx.showModal({
        title: '确认删除',
        content: '确定要删除这条帖子吗？删除后无法恢复。',
        success: async (res) => {
          if (res.confirm) {
            wx.showLoading({ title: '删除中...' });
            const result = await request({
              url: `/blade-chat/post/${id}`,
              method: 'DELETE'
            });
            wx.hideLoading();
            if (result.code === 200) {
              wx.showToast({ title: '删除成功', icon: 'success' });
              this.setData({ page: 1, posts: [], noMore: false }, () => { 
                this.loadPosts(); 
              });
            } else {
              wx.showToast({ title: result.msg || '删除失败', icon: 'none' });
            }
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('删除帖子失败:', error);
      wx.showToast({ title: '删除失败', icon: 'none' });
    }
  },
  
  async onMoveDraft(e) {
    const id = e.detail.id;
    try {
      wx.showModal({
        title: '确认移动',
        content: '确定要将这条帖子移动到草稿箱吗？',
        success: async (res) => {
          if (res.confirm) {
            wx.showLoading({ title: '移动中...' });
            const result = await request({
              url: `/blade-chat/post/move-draft/${id}`,
              method: 'POST'
            });
            wx.hideLoading();
            if (result.code === 200) {
              wx.showToast({ title: '已移到草稿箱', icon: 'success' });
              this.setData({ page: 1, posts: [], noMore: false }, () => { 
                this.loadPosts(); 
              });
            } else {
              wx.showToast({ title: result.msg || '移动失败', icon: 'none' });
            }
          }
        }
      });
    } catch (error) {
      wx.hideLoading();
      console.error('移动草稿失败:', error);
      wx.showToast({ title: '移动失败', icon: 'none' });
    }
  },
  
  async onCompletedPost(e) {
    const { id, completed } = e.detail;
    try {
      wx.showLoading({ title: '更新中...' });
      const result = await request({
        url: `/blade-chat/post/completed/${id}`,
        method: 'POST'
      });
      wx.hideLoading();
      if (result.code === 200) {
        const message = completed ? '已标记未完成' : '已标记已完成';
        wx.showToast({ title: message, icon: 'success' });
        this.setData({ page: 1, posts: [], noMore: false }, () => { 
          this.loadPosts(); 
        });
      } else {
        wx.showToast({ title: result.msg || '更新失败', icon: 'none' });
      }
    } catch (error) {
      wx.hideLoading();
      console.error('更新完成状态失败:', error);
      wx.showToast({ title: '更新失败', icon: 'none' });
    }
  }
}); 