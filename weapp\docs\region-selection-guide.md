# 微信小程序地址选择功能使用指南

## 功能概述

本功能提供了完整的地址选择解决方案，包括：
- 全局缓存管理
- 后台接口集成
- 开放城市查询
- 两级地区选择（市->区县）
- 搜索功能
- 自定义导航栏集成

## 核心组件

### 1. 地区缓存管理 (`utils/regionCache.js`)

提供地区数据的缓存、获取、清理等功能：

```javascript
const { regionCache } = require('../../utils/regionCache');

// 获取城市列表（所有开放的城市）
const cities = await regionCache.getCities();

// 获取区县列表
const districts = await regionCache.getDistricts(cityCode);

// 搜索地区（仅搜索城市和区县）
const results = await regionCache.searchRegions(keyword);

// 设置/获取当前位置
regionCache.setCurrentLocation(locationInfo);
const currentLocation = regionCache.getCurrentLocation();

// 清除缓存
regionCache.clearAllCache();
```

### 2. 地区选择器组件 (`components/region-picker`)

可复用的地区选择器组件：

```xml
<region-picker
  show="{{showRegionPicker}}"
  max-level="{{3}}"
  default-value="{{defaultRegionCode}}"
  bind:confirm="onRegionConfirm"
  bind:close="onRegionClose"
  bind:showchange="onRegionShowChange">
</region-picker>
```

**属性说明：**
- `show`: 是否显示选择器
- `max-level`: 最大选择层级（固定为3，只选择到区县）
- `default-value`: 默认选中的地区编码

**事件说明：**
- `confirm`: 选择确认事件
- `close`: 关闭事件
- `showchange`: 显示状态变化事件

### 3. 自定义导航栏 (`components/custom-nav`)

集成了地址选择功能的导航栏：

```xml
<custom-nav
  title="页面标题"
  show-location="{{true}}"
  default-region-code="{{defaultRegionCode}}"
  bind:locationChange="onLocationChange">
</custom-nav>
```

## 使用步骤

### 1. 在页面中引入组件

```json
{
  "usingComponents": {
    "custom-nav": "../../components/custom-nav/index",
    "region-picker": "../../components/region-picker/index"
  }
}
```

### 2. 在页面中使用

```javascript
Page({
  data: {
    showRegionPicker: false,
    selectedRegion: null
  },

  // 显示地区选择器
  showRegionSelector() {
    this.setData({ showRegionPicker: true });
  },

  // 地区选择确认
  onRegionConfirm(e) {
    const { selectedRegions, lastRegion, fullAddress } = e.detail;
    
    this.setData({
      selectedRegion: lastRegion,
      showRegionPicker: false
    });

    // 处理选择结果
    console.log('选择的地区:', fullAddress);
  },

  // 关闭地区选择器
  onRegionClose() {
    this.setData({ showRegionPicker: false });
  }
});
```

### 3. 在模板中使用

```xml
<!-- 使用自定义导航栏 -->
<custom-nav
  show-location="{{true}}"
  bind:locationChange="onLocationChange">
</custom-nav>

<!-- 或者单独使用地区选择器 -->
<button bindtap="showRegionSelector">选择地区</button>

<region-picker
  show="{{showRegionPicker}}"
  max-level="{{3}}"
  bind:confirm="onRegionConfirm"
  bind:close="onRegionClose">
</region-picker>
```

## 后台接口

### 接口列表

1. **获取城市列表**
   - URL: `/miniapp/region/cities`
   - 方法: GET
   - 返回: 所有开放的城市列表

2. **获取区县列表**
   - URL: `/miniapp/region/districts?cityCode={cityCode}`
   - 方法: GET
   - 返回: 指定城市下的开放区县列表

3. **获取完整地址信息**
   - URL: `/miniapp/region/full-address?regionCode={regionCode}`
   - 方法: GET
   - 返回: 指定地区编码的完整地址信息

4. **搜索地区**
   - URL: `/miniapp/region/search?keyword={keyword}`
   - 方法: GET
   - 返回: 搜索结果列表（仅包含城市和区县）

## 缓存机制

### 缓存策略
- 缓存时间：24小时
- 缓存键名：按层级和父级编码区分
- 自动过期：超时自动清理
- 手动清理：提供清理接口

### 缓存优化
- 优先从缓存获取数据
- 缓存未命中时调用API
- 支持批量清理缓存
- 内存占用优化

## 最佳实践

### 1. 性能优化
```javascript
// 预加载城市数据
onLoad() {
  regionCache.getCities();
}

// 延迟加载区县数据
async loadDistricts(cityCode) {
  const districts = await regionCache.getDistricts(cityCode);
}
```

### 2. 错误处理
```javascript
try {
  const cities = await regionCache.getCities();
  if (cities.length === 0) {
    wx.showToast({ title: '暂无开放城市', icon: 'none' });
  }
} catch (error) {
  console.error('加载城市数据失败:', error);
  wx.showToast({ title: '加载失败', icon: 'none' });
}
```

### 3. 用户体验
```javascript
// 显示加载状态
wx.showLoading({ title: '加载中...' });

// 提供搜索功能
const results = await regionCache.searchRegions(keyword);

// 记住用户选择
regionCache.setCurrentLocation(locationInfo);
```

## 注意事项

1. **数据同步**：确保后台已同步地区数据并设置开放状态
2. **网络处理**：做好网络异常的错误处理
3. **缓存管理**：适时清理过期缓存，避免内存泄漏
4. **用户体验**：提供加载状态和错误提示
5. **权限控制**：确保只显示开放的地区

## 演示页面

参考 `pages/region-demo` 目录下的完整演示实现，包含了所有功能的使用示例。
