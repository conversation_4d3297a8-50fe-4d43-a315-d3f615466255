const { login, getUserInfo } = require('../../utils/auth');

Page({
  data: {
    loading: false,
    checked: false
  },

  onLoad() {
    // 检查是否已登录
    const userInfo = getUserInfo();
    if (userInfo) {
      this.navigateBack();
      return;
    }
  },

  // 处理勾选协议
  onCheckChange(e) {
    this.setData({ checked: e.detail.value.length > 0 });
  },

  // 跳转协议页面
  onProtocolTap(e) {
    const type = e.currentTarget.dataset.type;
    wx.navigateTo({
      url: `/pages/login/protocol/protocol?type=${type}`
    });
  },

  // 处理登录
  async handleLogin() {
    if (this.data.loading) return;
    if (!this.data.checked) {
      wx.showToast({
        title: '请先同意协议',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ loading: true });
    
    try {
      // 执行登录
      await login();
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 延迟返回并刷新页面
      setTimeout(() => {
        const pages = getCurrentPages();
        const prevPage = pages[pages.length - 2];
        
        // 如果有上一页，则返回并刷新
        if (prevPage) {
          // 调用上一页的刷新方法
          if (typeof prevPage.onRefresh === 'function') {
            prevPage.onRefresh();
          } else if (typeof prevPage.onLoad === 'function') {
            prevPage.onLoad();
          } else if (typeof prevPage.onShow === 'function') {
            prevPage.onShow();
          }
          wx.navigateBack();
        } else {
          // 没有上一页则跳转到首页
          wx.reLaunch({
            url: '/pages/index/index'
          });
        }
      }, 1500);
      
    } catch (error) {
      console.error('登录失败：', error);
      wx.showToast({
        title: error.message || '登录失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 返回上一页
  navigateBack() {
    const pages = getCurrentPages();
    if (pages.length > 1) {
      wx.navigateBack();
    } else {
      wx.switchTab({
        url: '/pages/mine/mine'
      });
    }
  }
}); 