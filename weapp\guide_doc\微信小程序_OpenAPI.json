{"openapi": "3.0.1", "info": {"title": "SpringBlade 接口文档系统", "description": "SpringBlade 接口文档系统", "termsOfService": "https://bladex.cn", "contact": {"name": "smallchill", "url": "https://gitee.com/smallc", "email": "<EMAIL>"}, "license": {"name": "Powered By SpringBlade", "url": "https://bladex.cn"}, "version": "4.4.0"}, "servers": [{"url": "http://localhost", "description": "Generated server url"}], "tags": [{"name": "小程序-消息模块", "description": "消息管理相关接口"}, {"name": "小程序发布", "description": "小程序发布相关接口"}, {"name": "小程序签到接口", "description": "小程序签到相关接口"}, {"name": "微信用户信息", "description": "用户信息接口"}], "paths": {"/miniapp/post/{id}": {"put": {"tags": ["小程序发布"], "summary": "更新帖子", "description": "更新指定帖子", "operationId": "updatePost", "parameters": [{"name": "id", "in": "path", "description": "帖子ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostPublishDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSupPost"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/message/{id}/read": {"put": {"tags": ["小程序-消息模块"], "summary": "标记消息为已读", "description": "根据消息ID将消息状态设为已读", "operationId": "mark<PERSON><PERSON><PERSON>", "parameters": [{"name": "id", "in": "path", "description": "消息ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/message/templates": {"get": {"tags": ["小程序-消息模块"], "summary": "获取所有消息模板", "description": "返回所有可用的消息模板列表", "operationId": "getTemplates", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMessageTemplateVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}, "put": {"tags": ["小程序-消息模块"], "summary": "更新消息模板", "description": "根据ID更新消息模板内容和状态", "operationId": "updateTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTemplateUpdateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}, "post": {"tags": ["小程序-消息模块"], "summary": "新增消息模板", "description": "创建一个新的消息模板", "operationId": "addTemplate", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MessageTemplateCreateDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/{id}/like": {"post": {"tags": ["小程序发布"], "summary": "点赞帖子", "description": "对指定帖子进行点赞", "operationId": "likePost", "parameters": [{"name": "id", "in": "path", "description": "帖子ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "openId", "in": "query", "description": "用户OpenID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/{id}/favorite": {"post": {"tags": ["小程序发布"], "summary": "收藏帖子", "description": "收藏指定帖子", "operationId": "favoritePost", "parameters": [{"name": "id", "in": "path", "description": "帖子ID", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "openId", "in": "query", "description": "用户OpenID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/publish": {"post": {"tags": ["小程序发布"], "summary": "发布帖子", "description": "小程序用户发布帖子", "operationId": "publish", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostPublishDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSupPost"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/user/update": {"post": {"tags": ["微信用户信息"], "operationId": "update", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfile"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/signin/makeup": {"post": {"tags": ["小程序签到接口"], "summary": "补签", "description": "用户补签指定日期", "operationId": "makeUpSignin", "parameters": [{"name": "date", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSigninVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/signin/do": {"post": {"tags": ["小程序签到接口"], "summary": "执行签到", "description": "用户执行签到操作", "operationId": "doSignin", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SigninDTO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSigninVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/tag/custom": {"post": {"tags": ["小程序广告接口"], "summary": "查询分类的标签", "operationId": "tags", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TagCreateCommand"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/submit": {"post": {"tags": ["小程序广告接口"], "summary": "发布广告", "operationId": "submitPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupPostVO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/share": {"post": {"tags": ["小程序广告接口"], "summary": "记录分享行为", "operationId": "recordShare", "parameters": [{"name": "postId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/save-draft": {"post": {"tags": ["小程序广告接口"], "summary": "保存草稿", "operationId": "saveDraft", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupPostVO"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RLong"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/like/{postId}": {"post": {"tags": ["小程序广告接口"], "summary": "点赞帖子", "operationId": "likePost_1", "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/favorite/{postId}": {"post": {"tags": ["小程序广告接口"], "summary": "收藏帖子", "operationId": "favoritePost_1", "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/favorite/clear": {"post": {"tags": ["小程序广告接口"], "summary": "清空收藏", "operationId": "clearFavorites", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/create": {"post": {"tags": ["小程序广告接口"], "summary": "新增帖子", "operationId": "createPost", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PostCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSupPost"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/completed/{id}": {"post": {"tags": ["小程序广告接口"], "summary": "切换帖子完成状态", "operationId": "toggleCompleted", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/clear-view-history": {"post": {"tags": ["小程序广告接口"], "summary": "清空浏览记录", "operationId": "clearViewHistory", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/clear-call-history": {"post": {"tags": ["小程序拨号记录接口"], "summary": "清空拨号记录", "operationId": "clearCallHistory", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/add-call-history/{postId}": {"post": {"tags": ["小程序拨号记录接口"], "summary": "添加拨号记录", "operationId": "addCallHistory", "parameters": [{"name": "postId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/feedback/submit": {"post": {"tags": ["小程序反馈记录接口"], "summary": "提交反馈", "operationId": "submit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Feedback"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RLong"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/feedback/save": {"post": {"tags": ["小程序反馈记录接口"], "summary": "新增", "description": "传入report", "operationId": "save", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Report"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RLong"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 4}}, "/blade-chat/feedback/report/submit": {"post": {"tags": ["小程序反馈记录接口"], "summary": "新增或修改", "description": "传入report", "operationId": "submit_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Report"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RLong"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 6}}, "/blade-chat/feedback/painpoint/save": {"post": {"tags": ["小程序反馈记录接口"], "summary": "新增", "description": "传入painPoint", "operationId": "save_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PainPoint"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 4}}, "/blade-chat/feedback/helpful/{id}": {"post": {"tags": ["小程序反馈记录接口"], "summary": "标记反馈有帮助", "operationId": "toggleHelpful", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/tags/{categoryId}": {"get": {"tags": ["小程序发布"], "summary": "根据分类获取标签", "description": "根据分类ID获取该分类下的标签", "operationId": "getTagsByCategory", "parameters": [{"name": "categoryId", "in": "path", "description": "分类ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListTag"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/my-posts": {"get": {"tags": ["小程序发布"], "summary": "获取我的帖子", "description": "获取当前用户的帖子列表", "operationId": "getMyPosts", "parameters": [{"name": "current", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/list": {"get": {"tags": ["小程序发布"], "summary": "获取帖子列表", "description": "分页获取帖子列表", "operationId": "getPostList", "parameters": [{"name": "categoryId", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "keyword", "in": "query", "description": "关键词", "required": false, "schema": {"type": "string"}}, {"name": "current", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/miniapp/post/favorites": {"get": {"tags": ["小程序发布"], "summary": "获取收藏的帖子", "description": "获取用户收藏的帖子列表", "operationId": "getFavoritePosts", "parameters": [{"name": "openId", "in": "query", "description": "用户OpenID", "required": true, "schema": {"type": "string"}}, {"name": "current", "in": "query", "description": "页码", "required": false, "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "size", "in": "query", "description": "每页大小", "required": false, "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/user/stats": {"get": {"tags": ["微信用户信息"], "summary": "获取用户统计数据", "description": "获取用户的帖子数、点赞数、收藏数", "operationId": "getUserStats", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/user/post/personal-list": {"get": {"tags": ["微信用户信息"], "summary": "获取帖子列表", "operationId": "getPostListByUserId", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/user/info": {"get": {"tags": ["微信用户信息"], "summary": "详情", "description": "通过ID查询用户个人信息", "operationId": "info", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RUserProfile"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 1}}, "/blade-chat/user/group/page": {"get": {"tags": ["微信用户信息"], "summary": "分页", "description": "传入groupInfo", "operationId": "page", "parameters": [{"name": "groupName", "in": "query", "description": "群名称", "required": false, "schema": {"type": "string", "description": "群名称"}}, {"name": "groupType", "in": "query", "description": "群类型", "required": false, "schema": {"type": "string", "description": "群类型"}}, {"name": "groupImage", "in": "query", "description": "群图片", "required": false, "schema": {"type": "string", "description": "群图片"}}, {"name": "groupWeixin", "in": "query", "description": "微信链接", "required": false, "schema": {"type": "string", "description": "微信链接"}}, {"name": "groupDesc", "in": "query", "description": "群描述信息", "required": false, "schema": {"type": "string", "description": "群描述信息"}}, {"name": "regionCode", "in": "query", "description": "地区编号", "required": false, "schema": {"type": "string", "description": "地区编号"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageGroupInfoVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 3}}, "/blade-chat/user/group/detail": {"get": {"tags": ["微信用户信息"], "summary": "详情", "description": "传入groupInfo", "operationId": "detail", "parameters": [{"name": "groupName", "in": "query", "description": "群名称", "required": false, "schema": {"type": "string", "description": "群名称"}}, {"name": "groupType", "in": "query", "description": "群类型", "required": false, "schema": {"type": "string", "description": "群类型"}}, {"name": "groupImage", "in": "query", "description": "群图片", "required": false, "schema": {"type": "string", "description": "群图片"}}, {"name": "groupWeixin", "in": "query", "description": "微信链接", "required": false, "schema": {"type": "string", "description": "微信链接"}}, {"name": "groupDesc", "in": "query", "description": "群描述信息", "required": false, "schema": {"type": "string", "description": "群描述信息"}}, {"name": "regionCode", "in": "query", "description": "地区编号", "required": false, "schema": {"type": "string", "description": "地区编号"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RGroupInfoVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 1}}, "/blade-chat/user/feedback/list": {"get": {"tags": ["微信用户信息"], "summary": "获取用户反馈列表", "operationId": "getFeedbackList", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageFeedbackVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/signin/stats": {"get": {"tags": ["小程序签到接口"], "summary": "获取签到统计", "description": "获取用户签到统计信息", "operationId": "getSigninStats", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/signin/rewards": {"get": {"tags": ["小程序签到接口"], "summary": "获取连续签到奖励配置", "description": "获取连续签到奖励配置", "operationId": "getContinuousRewards", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/signin/record": {"get": {"tags": ["小程序签到接口"], "summary": "获取月签到记录", "description": "获取用户某月的签到记录", "operationId": "getMonthSigninRecord", "parameters": [{"name": "year", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "month", "in": "query", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/signin/info": {"get": {"tags": ["小程序签到接口"], "summary": "获取签到信息", "description": "获取用户签到信息", "operationId": "getSigninInfo", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListSigninRecordVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/view-history": {"get": {"tags": ["小程序广告接口"], "summary": "获取浏览记录", "description": "分页获取浏览记录", "operationId": "getViewHistory", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/tag/category/{categoryId}": {"get": {"tags": ["小程序广告接口"], "summary": "查询分类的标签", "operationId": "tags_1", "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListTag"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/my": {"get": {"tags": ["小程序广告接口"], "operationId": "getMyPosts_1", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/liked": {"get": {"tags": ["小程序广告接口"], "operationId": "getLikedPosts", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/favorite": {"get": {"tags": ["小程序广告接口"], "operationId": "getFavoritePosts_1", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/draft/{id}": {"get": {"tags": ["小程序广告接口"], "summary": "获取草稿详情", "operationId": "getDraftDetail", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}, "delete": {"tags": ["小程序广告接口"], "summary": "删除草稿", "operationId": "deleteDraft", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/draft-list": {"get": {"tags": ["小程序广告接口"], "summary": "获取草稿列表", "operationId": "getDraftList", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/call-history": {"get": {"tags": ["小程序拨号记录接口"], "summary": "获取拨号记录", "operationId": "getCallHistory", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/message": {"get": {"tags": ["小程序-消息模块"], "summary": "分页查询消息列表", "description": "支持标题、内容、类型、已读状态、时间范围等多条件分页查询", "operationId": "list", "parameters": [{"name": "userId", "in": "query", "description": "用户id", "required": false, "schema": {"type": "string", "description": "用户id"}}, {"name": "title", "in": "query", "description": "消息标题", "required": false, "schema": {"type": "string", "description": "消息标题"}}, {"name": "content", "in": "query", "description": "消息内容", "required": false, "schema": {"type": "string", "description": "消息内容"}}, {"name": "messageType", "in": "query", "description": "消息类型", "required": false, "schema": {"type": "string", "description": "消息类型"}}, {"name": "isRead", "in": "query", "description": "是否已读", "required": false, "schema": {"type": "string", "description": "是否已读"}}, {"name": "startTime", "in": "query", "description": "开始时间（格式：yyyy-MM-dd HH:mm:ss）", "required": false, "schema": {"type": "string", "description": "开始时间（格式：yyyy-MM-dd HH:mm:ss）"}}, {"name": "endTime", "in": "query", "description": "结束时间（格式：yyyy-MM-dd HH:mm:ss）", "required": false, "schema": {"type": "string", "description": "结束时间（格式：yyyy-MM-dd HH:mm:ss）"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageMessageVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}, "delete": {"tags": ["小程序-消息模块"], "summary": "批量删除消息", "description": "根据ID列表批量删除消息", "operationId": "batchDelete", "requestBody": {"content": {"application/json": {"schema": {"type": "array", "description": "消息ID列表", "items": {"type": "integer", "format": "int64"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/feedback/page": {"get": {"tags": ["小程序反馈记录接口"], "summary": "分页查询", "operationId": "page_1", "parameters": [{"name": "helpfulCount", "in": "query", "description": "有帮助标记数量", "required": false, "schema": {"type": "string", "description": "有帮助标记数量"}}, {"name": "nickname", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "avatar", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "categoryName", "in": "query", "description": "帖子分类", "required": false, "schema": {"type": "string", "description": "帖子分类"}}, {"name": "feedbackTime", "in": "query", "description": "反馈时间", "required": false, "schema": {"type": "string", "description": "反馈时间"}}, {"name": "post.location", "in": "query", "description": "地址", "required": false, "schema": {"type": "string", "description": "地址"}}, {"name": "post.longitude", "in": "query", "description": "经度", "required": false, "schema": {"type": "string", "description": "经度"}}, {"name": "post.latitude", "in": "query", "description": "纬度", "required": false, "schema": {"type": "string", "description": "纬度"}}, {"name": "post.contactType", "in": "query", "description": "联系信息类型", "required": false, "schema": {"type": "string", "description": "联系信息类型"}}, {"name": "post.contactNumber", "in": "query", "description": "联系编号", "required": false, "schema": {"type": "string", "description": "联系编号"}}, {"name": "post.title", "in": "query", "description": "标题", "required": false, "schema": {"type": "string", "description": "标题"}}, {"name": "post.content", "in": "query", "description": "内容", "required": false, "schema": {"type": "string", "description": "内容"}}, {"name": "post.images", "in": "query", "description": "图片", "required": false, "schema": {"type": "string", "description": "图片"}}, {"name": "post.address", "in": "query", "description": "发布地址", "required": false, "schema": {"type": "string", "description": "发布地址"}}, {"name": "post.publishTime", "in": "query", "description": "发布时间", "required": false, "schema": {"type": "string", "description": "发布时间"}}, {"name": "post.publishStatus", "in": "query", "description": "发布状态", "required": false, "schema": {"type": "string", "description": "发布状态"}}, {"name": "post.timeStatus", "in": "query", "description": "时效状态", "required": false, "schema": {"type": "string", "description": "时效状态"}}, {"name": "post.auditStatus", "in": "query", "description": "审核状态", "required": false, "schema": {"type": "string", "description": "审核状态"}}, {"name": "post.geoLocation", "in": "query", "description": "地理位置", "required": false, "schema": {"type": "string", "description": "地理位置"}}, {"name": "post.tags", "in": "query", "description": "标签", "required": false, "schema": {"type": "string", "description": "标签"}}, {"name": "post.contactName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "post.contactPhone", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "post.top", "in": "query", "description": "是否置顶", "required": false, "schema": {"type": "string", "description": "是否置顶"}}, {"name": "post.completed", "in": "query", "description": "是否已完成", "required": false, "schema": {"type": "string", "description": "是否已完成"}}, {"name": "post.categoryId", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "string", "description": "分类ID"}}, {"name": "post.auditRemark", "in": "query", "description": "审核备注", "required": false, "schema": {"type": "string", "description": "审核备注"}}, {"name": "post.auditTime", "in": "query", "description": "审核时间", "required": false, "schema": {"type": "string", "description": "审核时间"}}, {"name": "post.auditUserId", "in": "query", "description": "审核人ID", "required": false, "schema": {"type": "string", "description": "审核人ID"}}, {"name": "post.<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "是否启用审核", "required": false, "schema": {"type": "string", "description": "是否启用审核"}}, {"name": "post.isAnonymity", "in": "query", "description": "是否匿名", "required": false, "schema": {"type": "string", "description": "是否匿名"}}, {"name": "post.businessType", "in": "query", "description": "帖子类型", "required": false, "schema": {"type": "string", "description": "帖子类型"}}, {"name": "post.id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "postId", "in": "query", "description": "信息贴ID", "required": false, "schema": {"type": "string", "description": "信息贴ID"}}, {"name": "userId", "in": "query", "description": "反馈用户ID", "required": false, "schema": {"type": "string", "description": "反馈用户ID"}}, {"name": "content", "in": "query", "description": "反馈内容", "required": false, "schema": {"type": "string", "description": "反馈内容"}}, {"name": "auditStatus", "in": "query", "description": "审核状态", "required": false, "schema": {"type": "string", "description": "审核状态"}}, {"name": "reason", "in": "query", "description": "理由", "required": false, "schema": {"type": "string", "description": "理由"}}, {"name": "auditTime", "in": "query", "description": "审核时间", "required": false, "schema": {"type": "string", "description": "审核时间"}}, {"name": "auditUserId", "in": "query", "description": "审核人ID", "required": false, "schema": {"type": "string", "description": "审核人ID"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageFeedbackVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/feedback/getTagsByCategory": {"get": {"tags": ["小程序反馈记录接口"], "summary": "根据分类Id查询反馈标签", "description": "传入categoryId", "operationId": "getTagsByCategory_1", "parameters": [{"name": "categoryId", "in": "query", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListMapStringObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 9}}, "/blade-chat-open/post/list": {"get": {"tags": ["小程序广告开放接口"], "summary": "根据分类Id来条件分页查询帖子", "operationId": "getPostListByCategoryId", "parameters": [{"name": "postCategoryId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/post/home-list": {"get": {"tags": ["小程序广告开放接口"], "summary": "获取帖子列表", "operationId": "getHomePostList", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "viewTime", "in": "query", "required": false, "schema": {"type": "string", "format": "date-time"}}, {"name": "viewId", "in": "query", "required": false, "schema": {"type": "integer", "format": "int64"}}, {"name": "distance", "in": "query", "description": "距离 单位为km", "required": false, "schema": {"type": "string", "description": "距离 单位为km"}}, {"name": "carpool.id", "in": "query", "description": "ID", "required": false, "schema": {"type": "string", "description": "ID"}}, {"name": "carpool.region", "in": "query", "description": "所属区域", "required": false, "schema": {"type": "string", "description": "所属区域"}}, {"name": "carpool.carpoolType", "in": "query", "description": "拼车类型（如顺风车、拼货车等）", "required": false, "schema": {"type": "string", "description": "拼车类型（如顺风车、拼货车等）"}}, {"name": "carpool.departure", "in": "query", "description": "出发地", "required": false, "schema": {"type": "string", "description": "出发地"}}, {"name": "carpool.destination", "in": "query", "description": "目的地", "required": false, "schema": {"type": "string", "description": "目的地"}}, {"name": "carpool.via", "in": "query", "description": "途径地（可存多个，用逗号等分隔或 JSON 格式，根据实际解析需求定）", "required": false, "schema": {"type": "string", "description": "途径地（可存多个，用逗号等分隔或 JSON 格式，根据实际解析需求定）"}}, {"name": "carpool.departureTime", "in": "query", "description": "出发时间", "required": false, "schema": {"type": "string", "description": "出发时间"}}, {"name": "carpool.emptySeats", "in": "query", "description": "空位（人数场景）", "required": false, "schema": {"type": "string", "description": "空位（人数场景）"}}, {"name": "carpool.tonnage", "in": "query", "description": "数量(吨)，货车场景", "required": false, "schema": {"type": "string", "description": "数量(吨)，货车场景"}}, {"name": "carpool.postId", "in": "query", "description": "关联帖子表ID", "required": false, "schema": {"type": "string", "description": "关联帖子表ID"}}, {"name": "jobOffer.postId", "in": "query", "description": "关联帖子表ID", "required": false, "schema": {"type": "string", "description": "关联帖子表ID"}}, {"name": "jobOffer.recruitType", "in": "query", "description": "招聘类型", "required": false, "schema": {"type": "string", "description": "招聘类型"}}, {"name": "jobOffer.workplace", "in": "query", "description": "工作地址", "required": false, "schema": {"type": "string", "description": "工作地址"}}, {"name": "jobOffer.jobTitle", "in": "query", "description": "职位名称", "required": false, "schema": {"type": "string", "description": "职位名称"}}, {"name": "jobOffer.headcount", "in": "query", "description": "招聘人数", "required": false, "schema": {"type": "string", "description": "招聘人数"}}, {"name": "jobOffer.salary", "in": "query", "description": "工资", "required": false, "schema": {"type": "string", "description": "工资"}}, {"name": "jobOffer.jobKeywords", "in": "query", "description": "职位关键词", "required": false, "schema": {"type": "string", "description": "职位关键词"}}, {"name": "jobOffer.jobDescription", "in": "query", "description": "职位描述", "required": false, "schema": {"type": "string", "description": "职位描述"}}, {"name": "jobOffer.id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "jobSeeking.postId", "in": "query", "description": "关联帖子表ID", "required": false, "schema": {"type": "string", "description": "关联帖子表ID"}}, {"name": "jobSeeking.userId", "in": "query", "description": "用户ID", "required": false, "schema": {"type": "string", "description": "用户ID"}}, {"name": "jobSeeking.seekingType", "in": "query", "description": "求职类型", "required": false, "schema": {"type": "string", "description": "求职类型"}}, {"name": "jobSeeking.expectedPosition", "in": "query", "description": "期望岗位", "required": false, "schema": {"type": "string", "description": "期望岗位"}}, {"name": "jobSeeking.salaryExpectation", "in": "query", "description": "薪资要求", "required": false, "schema": {"type": "string", "description": "薪资要求"}}, {"name": "jobSeeking.positionPreference", "in": "query", "description": "职位偏好", "required": false, "schema": {"type": "string", "description": "职位偏好"}}, {"name": "jobSeeking.id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "user.nickname", "in": "query", "description": "昵称", "required": false, "schema": {"type": "string", "description": "昵称"}}, {"name": "user.mobile", "in": "query", "description": "手机号", "required": false, "schema": {"type": "string", "description": "手机号"}}, {"name": "user.gender", "in": "query", "description": "性别", "required": false, "schema": {"type": "string", "description": "性别"}}, {"name": "user.signature", "in": "query", "description": "个性签名", "required": false, "schema": {"type": "string", "description": "个性签名"}}, {"name": "user.avatar", "in": "query", "description": "头像", "required": false, "schema": {"type": "string", "description": "头像"}}, {"name": "user.birthday", "in": "query", "description": "生日", "required": false, "schema": {"type": "string", "description": "生日"}}, {"name": "user.region", "in": "query", "description": "地区", "required": false, "schema": {"type": "string", "description": "地区"}}, {"name": "user.email", "in": "query", "description": "邮箱", "required": false, "schema": {"type": "string", "description": "邮箱"}}, {"name": "user.age", "in": "query", "description": "年龄", "required": false, "schema": {"type": "string", "description": "年龄"}}, {"name": "user.tenantId", "in": "query", "description": "租户ID", "required": false, "schema": {"type": "string", "description": "租户ID"}}, {"name": "user.id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "category.name", "in": "query", "description": "分类名称", "required": false, "schema": {"type": "string", "description": "分类名称"}}, {"name": "category.parentId", "in": "query", "description": "上级分类ID", "required": false, "schema": {"type": "string", "description": "上级分类ID"}}, {"name": "category.icon", "in": "query", "description": "分类图标", "required": false, "schema": {"type": "string", "description": "分类图标"}}, {"name": "category.description", "in": "query", "description": "分类描述", "required": false, "schema": {"type": "string", "description": "分类描述"}}, {"name": "category.sort", "in": "query", "description": "排序", "required": false, "schema": {"type": "string", "description": "排序"}}, {"name": "category.enabled", "in": "query", "description": "是否启用", "required": false, "schema": {"type": "string", "description": "是否启用"}}, {"name": "category.enableAudit", "in": "query", "description": "是否启用审核", "required": false, "schema": {"type": "string", "description": "是否启用审核"}}, {"name": "category.tip", "in": "query", "description": "提示信息", "required": false, "schema": {"type": "string", "description": "提示信息"}}, {"name": "category.maxImages", "in": "query", "description": "最大图片数", "required": false, "schema": {"type": "string", "description": "最大图片数"}}, {"name": "category.allowTags", "in": "query", "description": "允许的标签", "required": false, "schema": {"type": "string", "description": "允许的标签"}}, {"name": "category.tags", "in": "query", "required": false, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}, {"name": "category.children", "in": "query", "required": false, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}}, {"name": "category.postCount", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "category.parentName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "category.id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "tagList", "in": "query", "required": false, "schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}}, {"name": "stats.isLiked", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "stats.isFavorite", "in": "query", "required": false, "schema": {"type": "boolean"}}, {"name": "stats.likeCount", "in": "query", "description": "点赞数", "required": false, "schema": {"type": "string", "description": "点赞数"}}, {"name": "stats.feedbackCount", "in": "query", "description": "反馈数量", "required": false, "schema": {"type": "string", "description": "反馈数量"}}, {"name": "stats.favoriteCount", "in": "query", "description": "收藏数", "required": false, "schema": {"type": "string", "description": "收藏数"}}, {"name": "stats.viewCount", "in": "query", "required": false, "schema": {"type": "integer", "format": "int32"}}, {"name": "location", "in": "query", "description": "地址", "required": false, "schema": {"type": "string", "description": "地址"}}, {"name": "longitude", "in": "query", "description": "经度", "required": false, "schema": {"type": "string", "description": "经度"}}, {"name": "latitude", "in": "query", "description": "纬度", "required": false, "schema": {"type": "string", "description": "纬度"}}, {"name": "contactType", "in": "query", "description": "联系信息类型", "required": false, "schema": {"type": "string", "description": "联系信息类型"}}, {"name": "contactNumber", "in": "query", "description": "联系编号", "required": false, "schema": {"type": "string", "description": "联系编号"}}, {"name": "title", "in": "query", "description": "标题", "required": false, "schema": {"type": "string", "description": "标题"}}, {"name": "content", "in": "query", "description": "内容", "required": false, "schema": {"type": "string", "description": "内容"}}, {"name": "images", "in": "query", "description": "图片", "required": false, "schema": {"type": "string", "description": "图片"}}, {"name": "address", "in": "query", "description": "发布地址", "required": false, "schema": {"type": "string", "description": "发布地址"}}, {"name": "publishTime", "in": "query", "description": "发布时间", "required": false, "schema": {"type": "string", "description": "发布时间"}}, {"name": "publishStatus", "in": "query", "description": "发布状态", "required": false, "schema": {"type": "string", "description": "发布状态"}}, {"name": "timeStatus", "in": "query", "description": "时效状态", "required": false, "schema": {"type": "string", "description": "时效状态"}}, {"name": "auditStatus", "in": "query", "description": "审核状态", "required": false, "schema": {"type": "string", "description": "审核状态"}}, {"name": "geoLocation", "in": "query", "description": "地理位置", "required": false, "schema": {"type": "string", "description": "地理位置"}}, {"name": "tags", "in": "query", "description": "标签", "required": false, "schema": {"type": "string", "description": "标签"}}, {"name": "contactName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "contactPhone", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "top", "in": "query", "description": "是否置顶", "required": false, "schema": {"type": "string", "description": "是否置顶"}}, {"name": "completed", "in": "query", "description": "是否已完成", "required": false, "schema": {"type": "string", "description": "是否已完成"}}, {"name": "categoryId", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "string", "description": "分类ID"}}, {"name": "auditRemark", "in": "query", "description": "审核备注", "required": false, "schema": {"type": "string", "description": "审核备注"}}, {"name": "auditTime", "in": "query", "description": "审核时间", "required": false, "schema": {"type": "string", "description": "审核时间"}}, {"name": "auditUserId", "in": "query", "description": "审核人ID", "required": false, "schema": {"type": "string", "description": "审核人ID"}}, {"name": "enable<PERSON><PERSON>t", "in": "query", "description": "是否启用审核", "required": false, "schema": {"type": "string", "description": "是否启用审核"}}, {"name": "isAnonymity", "in": "query", "description": "是否匿名", "required": false, "schema": {"type": "string", "description": "是否匿名"}}, {"name": "businessType", "in": "query", "description": "帖子类型", "required": false, "schema": {"type": "string", "description": "帖子类型"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/post/detail/{id}": {"get": {"tags": ["小程序广告开放接口"], "summary": "获取帖子详情", "operationId": "getPostDetail", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RSupPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/post/contact/{id}": {"get": {"tags": ["小程序广告开放接口"], "summary": "获取帖子联系方式", "operationId": "getPostContact", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RString"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/feedback/page": {"get": {"tags": ["小程序广告开放接口"], "summary": "分页查询", "operationId": "page_2", "parameters": [{"name": "helpfulCount", "in": "query", "description": "有帮助标记数量", "required": false, "schema": {"type": "string", "description": "有帮助标记数量"}}, {"name": "nickname", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "avatar", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "categoryName", "in": "query", "description": "帖子分类", "required": false, "schema": {"type": "string", "description": "帖子分类"}}, {"name": "feedbackTime", "in": "query", "description": "反馈时间", "required": false, "schema": {"type": "string", "description": "反馈时间"}}, {"name": "post.location", "in": "query", "description": "地址", "required": false, "schema": {"type": "string", "description": "地址"}}, {"name": "post.longitude", "in": "query", "description": "经度", "required": false, "schema": {"type": "string", "description": "经度"}}, {"name": "post.latitude", "in": "query", "description": "纬度", "required": false, "schema": {"type": "string", "description": "纬度"}}, {"name": "post.contactType", "in": "query", "description": "联系信息类型", "required": false, "schema": {"type": "string", "description": "联系信息类型"}}, {"name": "post.contactNumber", "in": "query", "description": "联系编号", "required": false, "schema": {"type": "string", "description": "联系编号"}}, {"name": "post.title", "in": "query", "description": "标题", "required": false, "schema": {"type": "string", "description": "标题"}}, {"name": "post.content", "in": "query", "description": "内容", "required": false, "schema": {"type": "string", "description": "内容"}}, {"name": "post.images", "in": "query", "description": "图片", "required": false, "schema": {"type": "string", "description": "图片"}}, {"name": "post.address", "in": "query", "description": "发布地址", "required": false, "schema": {"type": "string", "description": "发布地址"}}, {"name": "post.publishTime", "in": "query", "description": "发布时间", "required": false, "schema": {"type": "string", "description": "发布时间"}}, {"name": "post.publishStatus", "in": "query", "description": "发布状态", "required": false, "schema": {"type": "string", "description": "发布状态"}}, {"name": "post.timeStatus", "in": "query", "description": "时效状态", "required": false, "schema": {"type": "string", "description": "时效状态"}}, {"name": "post.auditStatus", "in": "query", "description": "审核状态", "required": false, "schema": {"type": "string", "description": "审核状态"}}, {"name": "post.geoLocation", "in": "query", "description": "地理位置", "required": false, "schema": {"type": "string", "description": "地理位置"}}, {"name": "post.tags", "in": "query", "description": "标签", "required": false, "schema": {"type": "string", "description": "标签"}}, {"name": "post.contactName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "post.contactPhone", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "post.top", "in": "query", "description": "是否置顶", "required": false, "schema": {"type": "string", "description": "是否置顶"}}, {"name": "post.completed", "in": "query", "description": "是否已完成", "required": false, "schema": {"type": "string", "description": "是否已完成"}}, {"name": "post.categoryId", "in": "query", "description": "分类ID", "required": false, "schema": {"type": "string", "description": "分类ID"}}, {"name": "post.auditRemark", "in": "query", "description": "审核备注", "required": false, "schema": {"type": "string", "description": "审核备注"}}, {"name": "post.auditTime", "in": "query", "description": "审核时间", "required": false, "schema": {"type": "string", "description": "审核时间"}}, {"name": "post.auditUserId", "in": "query", "description": "审核人ID", "required": false, "schema": {"type": "string", "description": "审核人ID"}}, {"name": "post.<PERSON><PERSON><PERSON><PERSON>", "in": "query", "description": "是否启用审核", "required": false, "schema": {"type": "string", "description": "是否启用审核"}}, {"name": "post.isAnonymity", "in": "query", "description": "是否匿名", "required": false, "schema": {"type": "string", "description": "是否匿名"}}, {"name": "post.businessType", "in": "query", "description": "帖子类型", "required": false, "schema": {"type": "string", "description": "帖子类型"}}, {"name": "post.id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "postId", "in": "query", "description": "信息贴ID", "required": false, "schema": {"type": "string", "description": "信息贴ID"}}, {"name": "userId", "in": "query", "description": "反馈用户ID", "required": false, "schema": {"type": "string", "description": "反馈用户ID"}}, {"name": "content", "in": "query", "description": "反馈内容", "required": false, "schema": {"type": "string", "description": "反馈内容"}}, {"name": "auditStatus", "in": "query", "description": "审核状态", "required": false, "schema": {"type": "string", "description": "审核状态"}}, {"name": "reason", "in": "query", "description": "理由", "required": false, "schema": {"type": "string", "description": "理由"}}, {"name": "auditTime", "in": "query", "description": "审核时间", "required": false, "schema": {"type": "string", "description": "审核时间"}}, {"name": "auditUserId", "in": "query", "description": "审核人ID", "required": false, "schema": {"type": "string", "description": "审核人ID"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageFeedbackVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/config/menu": {"get": {"tags": ["小程序广告开放接口"], "summary": "获取菜单和轮播图", "description": "返回菜单(category=0)和轮播图(category=1)，均按sort_weight升序排序", "operationId": "getMenusAndBanners", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RMapStringListUrbMenuVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/config/category": {"get": {"tags": ["小程序广告开放接口"], "operationId": "getCategoryList", "parameters": [{"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageCategoryVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat-open/categories": {"get": {"tags": ["小程序广告开放接口"], "summary": "获取分类列表", "description": "获取所有启用的分类", "operationId": "getCategories", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListCategory"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/{id}": {"delete": {"tags": ["小程序广告接口"], "summary": "删除帖子", "operationId": "deletePost", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/post/view-history/{id}": {"delete": {"tags": ["小程序广告接口"], "summary": "删除浏览记录", "operationId": "deleteViewHistory", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/feedback/remove": {"delete": {"tags": ["小程序反馈记录接口"], "summary": "删除反馈", "operationId": "removeMore", "parameters": [{"name": "ids", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-chat/feedback/remove/{id}": {"delete": {"tags": ["小程序反馈记录接口"], "summary": "删除反馈", "operationId": "remove", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}}, "components": {"schemas": {"R": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "PostCarpool": {"type": "object", "properties": {"id": {"type": "integer", "description": "ID", "format": "int64"}, "region": {"type": "string", "description": "所属区域"}, "carpoolType": {"type": "string", "description": "拼车类型（如顺风车、拼货车等）"}, "departure": {"type": "string", "description": "出发地"}, "destination": {"type": "string", "description": "目的地"}, "via": {"type": "string", "description": "途径地（可存多个，用逗号等分隔或 JSON 格式，根据实际解析需求定）"}, "departureTime": {"type": "string", "description": "出发时间", "format": "date-time"}, "emptySeats": {"type": "integer", "description": "空位（人数场景）", "format": "int64"}, "tonnage": {"type": "number", "description": "数量(吨)，货车场景"}, "postId": {"type": "integer", "description": "关联帖子表ID", "format": "int64"}}, "description": "顺风车信息表"}, "PostPublishDTO": {"required": ["categoryId", "content", "openId", "title"], "type": "object", "properties": {"title": {"type": "string", "description": "标题"}, "content": {"type": "string", "description": "内容"}, "images": {"type": "array", "description": "图片列表", "items": {"type": "string", "description": "图片列表"}}, "address": {"type": "string", "description": "发布地址"}, "geoLocation": {"type": "string", "description": "地理位置"}, "tags": {"type": "array", "description": "标签列表", "items": {"type": "string", "description": "标签列表"}}, "contactName": {"type": "string", "description": "联系人姓名"}, "contactPhone": {"type": "string", "description": "联系电话"}, "categoryId": {"type": "integer", "description": "分类ID", "format": "int64"}, "openId": {"type": "string", "description": "小程序用户OpenID"}, "nickName": {"type": "string", "description": "用户昵称"}, "avatarUrl": {"type": "string", "description": "用户头像"}, "isAnonymity": {"type": "string", "description": "是否为匿名"}, "carpool": {"$ref": "#/components/schemas/PostCarpool"}}, "description": "小程序发布帖子DTO"}, "RSupPost": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/SupPost"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "SupPost": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "location": {"type": "string", "description": "地址"}, "longitude": {"type": "number", "description": "经度", "format": "double"}, "latitude": {"type": "number", "description": "纬度", "format": "double"}, "contactType": {"type": "string", "description": "联系信息类型"}, "contactNumber": {"type": "string", "description": "联系编号"}, "title": {"type": "string", "description": "标题"}, "content": {"type": "string", "description": "内容"}, "images": {"type": "string", "description": "图片"}, "address": {"type": "string", "description": "发布地址"}, "publishTime": {"type": "string", "description": "发布时间", "format": "date-time"}, "publishStatus": {"type": "string", "description": "发布状态"}, "timeStatus": {"type": "string", "description": "时效状态"}, "auditStatus": {"type": "string", "description": "审核状态"}, "geoLocation": {"type": "string", "description": "地理位置"}, "tags": {"type": "array", "description": "标签", "items": {"type": "string", "description": "标签"}}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "top": {"type": "string", "description": "是否置顶"}, "completed": {"type": "integer", "description": "是否已完成", "format": "int32"}, "categoryId": {"type": "integer", "description": "分类ID", "format": "int64"}, "auditRemark": {"type": "string", "description": "审核备注"}, "auditTime": {"type": "string", "description": "审核时间", "format": "date-time"}, "auditUserId": {"type": "integer", "description": "审核人ID", "format": "int64"}, "enableAudit": {"type": "integer", "description": "是否启用审核", "format": "int32"}, "isAnonymity": {"type": "string", "description": "是否匿名"}, "businessType": {"type": "string", "description": "帖子类型"}}, "description": "百事通信息贴"}, "RObject": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "MessageTemplateUpdateDTO": {"required": ["id"], "type": "object", "properties": {"id": {"type": "integer", "description": "模板ID", "format": "int64"}, "titleTemplate": {"type": "string", "description": "消息标题模板"}, "contentTemplate": {"type": "string", "description": "消息内容模板"}, "status": {"type": "integer", "description": "模板状态(1:启用 0:禁用)", "format": "int32"}}, "description": "模板更新DTO"}, "RBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "boolean", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "UserProfile": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "tenantId": {"type": "string", "description": "租户ID"}, "name": {"type": "string", "description": "联系人姓名"}, "phone": {"type": "string", "description": "联系电话"}, "wechat": {"type": "string", "description": "微信账号"}, "contactType": {"type": "string", "description": "联系方式类型"}, "nickname": {"type": "string", "description": "昵称"}, "mobile": {"type": "string", "description": "手机号"}, "gender": {"type": "string", "description": "性别"}, "signature": {"type": "string", "description": "个性签名"}, "avatar": {"type": "string", "description": "头像"}, "birthday": {"type": "string", "description": "生日"}, "region": {"type": "string", "description": "地区"}, "email": {"type": "string", "description": "邮箱"}, "postCount": {"type": "integer", "description": "帖子总数", "format": "int64"}, "feedbackCount": {"type": "integer", "description": "反馈总数", "format": "int64"}}}, "RSigninVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/SigninVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "SigninVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "签到ID", "format": "int64", "refType": null}, "userId": {"type": "string", "description": "用户Id", "refType": null}, "signinDate": {"type": "string", "description": "签到日期", "format": "date", "refType": null}, "signinTime": {"type": "string", "description": "签到时间", "format": "date-time", "refType": null}, "points": {"type": "integer", "description": "获得积分", "format": "int32", "refType": null}, "continuousReward": {"type": "integer", "description": "连续签到奖励积分", "format": "int32", "refType": null}, "continuousDays": {"type": "integer", "description": "连续签到天数", "format": "int32", "refType": null}, "totalSigninDays": {"type": "integer", "description": "总签到天数", "format": "int32", "refType": null}, "currentPoints": {"type": "integer", "description": "当前积分余额", "format": "int32", "refType": null}, "signinType": {"type": "string", "description": "签到类型", "refType": null}, "todaySigned": {"type": "boolean", "description": "是否今日已签到", "refType": null}, "lastSigninTime": {"type": "string", "description": "最后签到时间", "refType": null}}, "description": "签到VO"}, "SigninDTO": {"type": "object", "properties": {"userId": {"type": "string", "description": "用户ID"}, "signinDate": {"type": "string", "description": "签到日期（补签时使用）"}, "signinType": {"type": "string", "description": "签到类型"}, "deviceInfo": {"type": "string", "description": "设备信息"}, "signinIp": {"type": "string", "description": "签到IP"}}, "description": "签到DTO"}, "TagCreateCommand": {"type": "object"}, "Category": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "name": {"type": "string", "description": "分类名称"}, "parentId": {"type": "integer", "description": "上级分类ID", "format": "int64"}, "icon": {"type": "string", "description": "分类图标"}, "description": {"type": "string", "description": "分类描述"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "enabled": {"type": "integer", "description": "是否启用", "format": "int32"}, "enableAudit": {"type": "integer", "description": "是否启用审核", "format": "int32"}, "tip": {"type": "string", "description": "提示信息"}, "maxImages": {"type": "integer", "description": "最大图片数", "format": "int32"}, "allowTags": {"type": "string", "description": "允许的标签"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "postCount": {"type": "integer", "format": "int32"}, "parentName": {"type": "string"}}, "description": "广告分类"}, "JobOffer": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "关联帖子表ID", "format": "int64"}, "recruitType": {"type": "string", "description": "招聘类型"}, "workplace": {"type": "string", "description": "工作地址"}, "jobTitle": {"type": "string", "description": "职位名称"}, "headcount": {"type": "integer", "description": "招聘人数", "format": "int32"}, "salary": {"type": "string", "description": "工资"}, "jobKeywords": {"type": "string", "description": "职位关键词"}, "jobDescription": {"type": "string", "description": "职位描述"}}, "description": "JobOffer对象"}, "JobSeeking": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "关联帖子表ID", "format": "int64"}, "userId": {"type": "integer", "description": "用户ID", "format": "int64"}, "seekingType": {"type": "string", "description": "求职类型"}, "expectedPosition": {"type": "string", "description": "期望岗位"}, "salaryExpectation": {"type": "string", "description": "薪资要求"}, "positionPreference": {"type": "string", "description": "职位偏好"}}, "description": "JobSeeking对象"}, "PostStatsDTO": {"type": "object", "properties": {"isLiked": {"type": "boolean"}, "isFavorite": {"type": "boolean"}, "likeCount": {"type": "integer", "description": "点赞数", "format": "int32"}, "feedbackCount": {"type": "integer", "description": "反馈数量", "format": "int32"}, "favoriteCount": {"type": "integer", "description": "收藏数", "format": "int32"}, "viewCount": {"type": "integer", "format": "int32"}}, "description": "帖子统计信息"}, "SupPostVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "location": {"type": "string", "description": "地址"}, "longitude": {"type": "number", "description": "经度", "format": "double"}, "latitude": {"type": "number", "description": "纬度", "format": "double"}, "contactType": {"type": "string", "description": "联系信息类型"}, "contactNumber": {"type": "string", "description": "联系编号"}, "title": {"type": "string", "description": "标题"}, "content": {"type": "string", "description": "内容"}, "images": {"type": "string", "description": "图片"}, "address": {"type": "string", "description": "发布地址"}, "publishTime": {"type": "string", "description": "发布时间", "format": "date-time"}, "publishStatus": {"type": "string", "description": "发布状态"}, "timeStatus": {"type": "string", "description": "时效状态"}, "auditStatus": {"type": "string", "description": "审核状态"}, "geoLocation": {"type": "string", "description": "地理位置"}, "tags": {"type": "array", "description": "标签", "items": {"type": "string", "description": "标签"}}, "contactName": {"type": "string"}, "contactPhone": {"type": "string"}, "top": {"type": "string", "description": "是否置顶"}, "completed": {"type": "integer", "description": "是否已完成", "format": "int32"}, "categoryId": {"type": "integer", "description": "分类ID", "format": "int64"}, "auditRemark": {"type": "string", "description": "审核备注"}, "auditTime": {"type": "string", "description": "审核时间", "format": "date-time"}, "auditUserId": {"type": "integer", "description": "审核人ID", "format": "int64"}, "enableAudit": {"type": "integer", "description": "是否启用审核", "format": "int32"}, "isAnonymity": {"type": "string", "description": "是否匿名"}, "businessType": {"type": "string", "description": "帖子类型"}, "viewTime": {"type": "string", "format": "date-time"}, "viewId": {"type": "integer", "format": "int64"}, "distance": {"type": "number", "description": "距离 单位为km", "format": "double"}, "carpool": {"$ref": "#/components/schemas/PostCarpool"}, "jobOffer": {"$ref": "#/components/schemas/JobOffer"}, "jobSeeking": {"$ref": "#/components/schemas/JobSeeking"}, "user": {"$ref": "#/components/schemas/WeUser"}, "category": {"$ref": "#/components/schemas/Category"}, "tagList": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "stats": {"$ref": "#/components/schemas/PostStatsDTO"}}, "description": "百事通信息贴"}, "Tag": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "tagName": {"type": "string", "description": "标签名称"}, "categoryId": {"type": "integer", "description": "分类ID", "format": "int64"}, "color": {"type": "string", "description": "标签颜色"}, "icon": {"type": "string", "description": "标签图标"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "enabled": {"type": "integer", "description": "是否启用", "format": "int32"}, "useCount": {"type": "integer", "description": "使用次数", "format": "int32"}, "isSystem": {"type": "integer", "description": "是否系统标签", "format": "int32"}, "description": {"type": "string"}, "sortOrder": {"type": "string"}, "type": {"type": "integer", "format": "int32"}}, "description": "标签"}, "WeUser": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "tenantId": {"type": "string", "description": "租户ID"}, "nickname": {"type": "string", "description": "昵称"}, "mobile": {"type": "string", "description": "手机号"}, "gender": {"type": "string", "description": "性别"}, "signature": {"type": "string", "description": "个性签名"}, "avatar": {"type": "string", "description": "头像"}, "birthday": {"type": "string", "description": "生日"}, "region": {"type": "string", "description": "地区"}, "email": {"type": "string", "description": "邮箱"}, "age": {"type": "integer", "description": "年龄", "format": "int32"}}, "description": "用户信息"}, "RLong": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "integer", "description": "承载数据", "format": "int64"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "PostCreateRequest": {"type": "object", "properties": {"images": {"type": "string"}, "tags": {"type": "string"}, "contactName": {"type": "string"}, "contactType": {"type": "string"}, "contactNumber": {"type": "string"}, "content": {"type": "string"}, "categoryId": {"type": "integer", "format": "int64"}, "location": {"type": "string"}, "address": {"type": "string"}, "longitude": {"type": "number", "format": "double"}, "latitude": {"type": "number", "format": "double"}, "isAnonymity": {"type": "string"}, "businessType": {"type": "string", "description": "帖子类型"}, "carpool": {"$ref": "#/components/schemas/PostCarpool"}, "jobOffer": {"$ref": "#/components/schemas/JobOffer"}, "jobSeek": {"$ref": "#/components/schemas/JobSeeking"}, "rentHouse": {"$ref": "#/components/schemas/RentHouse"}, "usedCar": {"$ref": "#/components/schemas/UsedCar"}}}, "RentHouse": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "关联帖子Id", "format": "int64"}, "houseName": {"type": "string", "description": "房子名称"}, "rentType": {"type": "string", "description": "租房类型（合租，整租）"}, "houseType": {"type": "string", "description": "户型（3室，4室）"}, "bedroomType": {"type": "string", "description": "卧室类型（主卧，次卧）"}, "houseSize": {"type": "string", "description": "房子大小"}, "rentPrice": {"type": "string", "description": "租房价格"}, "type": {"type": "string", "description": "（1-出房，2-找房）"}}, "description": "群信息表"}, "UsedCar": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "关联帖子Id", "format": "int64"}, "carName": {"type": "string", "description": "车名"}, "carAge": {"type": "string", "description": "车年限"}, "carPrice": {"type": "string", "description": "车价"}, "carKm": {"type": "string", "description": "行驶公里数"}, "carConfiguration": {"type": "string", "description": "配置"}, "type": {"type": "string", "description": "（1-卖车，2-买车）"}}, "description": "售卖二手车表"}, "MessageTemplateCreateDTO": {"required": ["contentTemplate", "titleTemplate", "typeCode", "typeName"], "type": "object", "properties": {"typeCode": {"type": "string", "description": "消息类型编码"}, "typeName": {"type": "string", "description": "消息类型名称"}, "titleTemplate": {"type": "string", "description": "消息标题模板"}, "contentTemplate": {"type": "string", "description": "消息内容模板"}}, "description": "新增消息模板DTO"}, "Feedback": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "信息贴ID", "format": "int64"}, "userId": {"type": "integer", "description": "反馈用户ID", "format": "int64"}, "content": {"type": "string", "description": "反馈内容"}, "auditStatus": {"type": "string", "description": "审核状态"}, "reason": {"type": "string", "description": "理由"}, "categoryName": {"type": "string", "description": "帖子分类"}, "auditTime": {"type": "string", "description": "审核时间", "format": "date-time"}, "auditUserId": {"type": "integer", "description": "审核人ID", "format": "int64"}}, "description": "用户反馈"}, "Report": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "信息贴ID", "format": "int64"}, "userId": {"type": "integer", "description": "举报用户ID", "format": "int64"}, "content": {"type": "string", "description": "举报内容"}, "images": {"type": "string", "description": "举报图片"}, "auditStatus": {"type": "string", "description": "审核状态"}, "reason": {"type": "string", "description": "理由"}, "userName": {"type": "string", "description": "被举报用户名字"}, "userPhone": {"type": "string", "description": "被举报用户电话"}, "reportUserId": {"type": "integer", "description": "举报用户ID", "format": "int64"}, "reportUserName": {"type": "string", "description": "举报用户名字"}, "reportUserPhone": {"type": "string", "description": "举报用户电话"}, "reportCount": {"type": "integer", "description": "被举报次数", "format": "int32"}}, "description": "举报记录"}, "PainPoint": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "content": {"type": "string", "description": "反馈内容"}, "image": {"type": "string", "description": "图片路径或链接"}, "contactInfo": {"type": "string", "description": "联系方式"}, "auditStatus": {"type": "string", "description": "处理状态"}, "auditResult": {"type": "string", "description": "处理结果"}, "nickname": {"type": "string", "description": "反馈人姓名"}}, "description": "PainPoint对象"}, "RListTag": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/Tag"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "IPageSupPostVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/SupPostVO"}}, "current": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageSupPostVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageSupPostVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RMapStringObject": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "additionalProperties": {"type": "object", "description": "承载数据"}, "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RUserProfile": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/UserProfile"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "GroupInfoVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "groupName": {"type": "string", "description": "群名称"}, "groupType": {"type": "string", "description": "群类型"}, "groupImage": {"type": "string", "description": "群图片"}, "groupWeixin": {"type": "string", "description": "微信链接"}, "groupDesc": {"type": "string", "description": "群描述信息"}, "regionCode": {"type": "string", "description": "地区编号"}}, "description": "群信息表"}, "IPageGroupInfoVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/GroupInfoVO"}}, "current": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageGroupInfoVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageGroupInfoVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RGroupInfoVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/GroupInfoVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "FeedbackVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "postId": {"type": "integer", "description": "信息贴ID", "format": "int64"}, "userId": {"type": "integer", "description": "反馈用户ID", "format": "int64"}, "content": {"type": "string", "description": "反馈内容"}, "auditStatus": {"type": "string", "description": "审核状态"}, "reason": {"type": "string", "description": "理由"}, "categoryName": {"type": "string", "description": "帖子分类"}, "auditTime": {"type": "string", "description": "审核时间", "format": "date-time"}, "auditUserId": {"type": "integer", "description": "审核人ID", "format": "int64"}, "helpfulCount": {"type": "integer", "description": "有帮助标记数量", "format": "int32"}, "nickname": {"type": "string"}, "avatar": {"type": "string"}, "feedbackTime": {"type": "string", "description": "反馈时间", "format": "date-time"}, "post": {"$ref": "#/components/schemas/SupPost"}, "helpful": {"type": "boolean"}}, "description": "用户反馈"}, "IPageFeedbackVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/FeedbackVO"}}, "current": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageFeedbackVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageFeedbackVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RListMapStringObject": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"type": "object", "additionalProperties": {"type": "object", "description": "承载数据"}, "description": "承载数据"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RListSigninRecordVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/SigninRecordVO"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "SigninRecordVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "签到ID", "format": "int64"}, "userId": {"type": "string", "description": "用户Id"}, "signinDate": {"type": "string", "description": "签到日期", "format": "date"}, "signinTime": {"type": "string", "description": "签到时间", "format": "date-time"}, "points": {"type": "integer", "description": "获得积分", "format": "int32"}, "continuousReward": {"type": "integer", "description": "连续签到奖励积分", "format": "int32"}, "continuousDays": {"type": "integer", "description": "连续签到天数", "format": "int32"}, "totalSigninDays": {"type": "integer", "description": "总签到天数", "format": "int32"}, "currentPoints": {"type": "integer", "description": "当前积分余额", "format": "int32"}, "signinType": {"type": "string", "description": "签到类型"}, "todaySigned": {"type": "boolean", "description": "是否今日已签到"}, "lastSigninTime": {"type": "string", "description": "最后签到时间"}}, "description": "承载数据"}, "RSupPostVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/SupPostVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "IPageMessageVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/MessageVO"}}, "current": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "MessageVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "消息ID", "format": "int64"}, "title": {"type": "string", "description": "消息标题"}, "content": {"type": "string", "description": "消息内容"}, "messageType": {"type": "string", "description": "消息类型"}, "isRead": {"type": "boolean", "description": "是否已读"}, "relatedId": {"type": "integer", "description": "关联业务ID", "format": "int64"}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "description": "消息视图对象"}, "RIPageMessageVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageMessageVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "MessageTemplateVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键ID", "format": "int64"}, "typeCode": {"type": "string", "description": "消息类型编码"}, "typeName": {"type": "string", "description": "消息类型名称"}, "titleTemplate": {"type": "string", "description": "消息标题模板"}, "contentTemplate": {"type": "string", "description": "消息内容模板"}, "isSystem": {"type": "boolean", "description": "是否为系统内置模板"}, "status": {"type": "integer", "description": "模板状态(1:启用 0:禁用)", "format": "int32"}}, "description": "消息模板视图对象"}, "RListMessageTemplateVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/MessageTemplateVO"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RString": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "string", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RMapStringListUrbMenuVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "additionalProperties": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/UrbMenuVO"}}, "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "UrbMenuVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "name": {"type": "string", "description": "唯一，菜单名称"}, "image": {"type": "string", "description": "图片地址"}, "sortWeight": {"type": "string", "description": "排序权重"}, "color": {"type": "string", "description": "用于存储16进制颜色数据"}, "url": {"type": "string", "description": "用于存储链接地址"}, "category": {"type": "integer", "description": "0-菜单，1-滚动图片 2-用户菜单", "format": "int32"}}, "description": "UrbMenuVO对象"}, "CategoryVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "name": {"type": "string", "description": "分类名称"}, "parentId": {"type": "integer", "description": "上级分类ID", "format": "int64"}, "icon": {"type": "string", "description": "分类图标"}, "description": {"type": "string", "description": "分类描述"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "enabled": {"type": "integer", "description": "是否启用", "format": "int32"}, "enableAudit": {"type": "integer", "description": "是否启用审核", "format": "int32"}, "tip": {"type": "string", "description": "提示信息"}, "maxImages": {"type": "integer", "description": "最大图片数", "format": "int32"}, "allowTags": {"type": "string", "description": "允许的标签"}, "tags": {"type": "array", "items": {"$ref": "#/components/schemas/Tag"}}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/Category"}}, "postCount": {"type": "integer", "format": "int32"}, "parentName": {"type": "string"}}, "description": "广告分类"}, "IPageCategoryVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryVO"}}, "current": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageCategoryVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageCategoryVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RListCategory": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/Category"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}}, "securitySchemes": {"Blade-Auth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>-<PERSON><PERSON>", "in": "header"}, "Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "Tenant-Id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Tenant-Id", "in": "header"}}}, "x-openapi": {"x-setting": {"customCode": 200, "language": "zh-CN", "enableSwaggerModels": true, "swaggerModelName": "Swagger Models", "enableReloadCacheParameter": false, "enableAfterScript": true, "enableDocumentManage": true, "enableVersion": false, "enableRequestCache": true, "enableFilterMultipartApis": false, "enableFilterMultipartApiMethodType": "POST", "enableHost": false, "enableHostText": "http://localhost", "enableDynamicParameter": false, "enableDebug": true, "enableFooter": false, "enableFooterCustom": true, "footerCustomContent": "Copyright © 2024 SpringBlade All Rights Reserved", "enableSearch": true, "enableOpenApi": true, "enableHomeCustom": false, "enableGroup": true, "enableResponseCode": true}, "x-markdownFiles": []}}